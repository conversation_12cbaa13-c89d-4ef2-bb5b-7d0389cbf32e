package com.pokecobble.notification;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.network.NetworkConstants;
import com.pokecobble.town.network.NetworkManager;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking;
import net.fabricmc.fabric.api.networking.v1.PacketByteBufs;
import net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.util.Identifier;

import java.util.List;
import java.util.UUID;

/**
 * Handles synchronization of notifications between server and client.
 */
public class ServerNotificationSynchronizer {
    
    // Network identifiers
    public static final Identifier NOTIFICATION_SYNC = new Identifier("pokecobbleclaim", "notification_sync");
    public static final Identifier NOTIFICATION_MARK_READ = new Identifier("pokecobbleclaim", "notification_mark_read");
    public static final Identifier NOTIFICATION_REMOVE = new Identifier("pokecobbleclaim", "notification_remove");
    public static final Identifier NOTIFICATION_REQUEST = new Identifier("pokecobbleclaim", "notification_request");
    public static final Identifier NOTIFICATION_HISTORY_REQUEST = new Identifier("pokecobbleclaim", "notification_history_request");
    
    /**
     * Registers server-side packet handlers.
     */
    public static void registerServerHandlers() {
        // Register mark read handler
        ServerPlayNetworking.registerGlobalReceiver(NOTIFICATION_MARK_READ, ServerNotificationSynchronizer::handleMarkRead);
        
        // Register remove handler
        ServerPlayNetworking.registerGlobalReceiver(NOTIFICATION_REMOVE, ServerNotificationSynchronizer::handleRemove);
        
        // Register notification request handler
        ServerPlayNetworking.registerGlobalReceiver(NOTIFICATION_REQUEST, ServerNotificationSynchronizer::handleNotificationRequest);
        
        // Register history request handler
        ServerPlayNetworking.registerGlobalReceiver(NOTIFICATION_HISTORY_REQUEST, ServerNotificationSynchronizer::handleHistoryRequest);
    }
    
    /**
     * Registers client-side packet handlers.
     */
    @Environment(EnvType.CLIENT)
    public static void registerClientHandlers() {
        // Register notification sync handler
        ClientPlayNetworking.registerGlobalReceiver(NOTIFICATION_SYNC, ServerNotificationSynchronizer::handleNotificationSync);
    }
    
    /**
     * Synchronizes player notifications to the client.
     */
    public static void syncPlayerNotifications(UUID playerId) {
        try {
            // Get server instance (this would need to be passed or accessed differently)
            MinecraftServer server = com.pokecobble.Pokecobbleclaim.getServer();
            if (server == null) {
                return;
            }
            
            ServerPlayerEntity player = server.getPlayerManager().getPlayer(playerId);
            if (player == null) {
                return;
            }
            
            ServerNotificationManager manager = ServerNotificationManager.getInstance();
            List<ServerNotification> notifications = manager.getPlayerNotifications(playerId);
            
            PacketByteBuf buf = PacketByteBufs.create();
            
            // Write data version
            int version = manager.getPlayerNotificationVersion(playerId);
            buf.writeInt(version);
            
            // Write number of notifications
            buf.writeInt(notifications.size());
            
            // Write each notification
            for (ServerNotification notification : notifications) {
                buf.writeUuid(notification.getId());
                buf.writeUuid(notification.getPlayerId());
                buf.writeString(notification.getTitle(), NetworkConstants.MAX_STRING_LENGTH);
                buf.writeString(notification.getMessage(), NetworkConstants.MAX_STRING_LENGTH);
                buf.writeInt(notification.getType().ordinal());
                buf.writeInt(notification.getDurationTicks());
                buf.writeInt(notification.getRemainingTicks());
                buf.writeLong(notification.getCreatedTime());
                buf.writeBoolean(notification.isRead());
                buf.writeLong(notification.getReadTime());
                
                // Write optional town data
                buf.writeBoolean(notification.getTownName() != null);
                if (notification.getTownName() != null) {
                    buf.writeString(notification.getTownName(), NetworkConstants.MAX_STRING_LENGTH);
                }
                
                buf.writeBoolean(notification.getTownId() != null);
                if (notification.getTownId() != null) {
                    buf.writeUuid(notification.getTownId());
                }
            }
            
            // Send packet (bypass rate limiting for automatic notification sync)
            NetworkManager.sendToPlayer(player, NOTIFICATION_SYNC, buf);
            
            Pokecobbleclaim.LOGGER.debug("Synchronized " + notifications.size() + " notifications to player " + player.getName().getString());
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error synchronizing notifications to player " + playerId + ": " + e.getMessage());
        }
    }
    
    /**
     * Handles mark read request from client.
     */
    private static void handleMarkRead(MinecraftServer server, ServerPlayerEntity player,
                                     net.minecraft.server.network.ServerPlayNetworkHandler handler,
                                     PacketByteBuf buf,
                                     net.fabricmc.fabric.api.networking.v1.PacketSender sender) {
        try {
            UUID notificationId = buf.readUuid();
            UUID playerId = player.getUuid();
            
            ServerNotificationManager manager = ServerNotificationManager.getInstance();
            boolean marked = manager.markNotificationAsRead(playerId, notificationId);
            
            if (marked) {
                Pokecobbleclaim.LOGGER.debug("Marked notification " + notificationId + " as read for player " + player.getName().getString());
            }
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling mark read request: " + e.getMessage());
        }
    }
    
    /**
     * Handles remove notification request from client.
     */
    private static void handleRemove(MinecraftServer server, ServerPlayerEntity player,
                                   net.minecraft.server.network.ServerPlayNetworkHandler handler,
                                   PacketByteBuf buf,
                                   net.fabricmc.fabric.api.networking.v1.PacketSender sender) {
        try {
            UUID notificationId = buf.readUuid();
            UUID playerId = player.getUuid();
            
            ServerNotificationManager manager = ServerNotificationManager.getInstance();
            boolean removed = manager.removeNotification(playerId, notificationId);
            
            if (removed) {
                Pokecobbleclaim.LOGGER.debug("Removed notification " + notificationId + " for player " + player.getName().getString());
            }
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling remove notification request: " + e.getMessage());
        }
    }
    
    /**
     * Handles notification request from client.
     */
    private static void handleNotificationRequest(MinecraftServer server, ServerPlayerEntity player,
                                                net.minecraft.server.network.ServerPlayNetworkHandler handler,
                                                PacketByteBuf buf,
                                                net.fabricmc.fabric.api.networking.v1.PacketSender sender) {
        try {
            UUID playerId = player.getUuid();
            
            // Sync current notifications
            syncPlayerNotifications(playerId);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling notification request: " + e.getMessage());
        }
    }
    
    /**
     * Handles notification history request from client.
     */
    private static void handleHistoryRequest(MinecraftServer server, ServerPlayerEntity player,
                                           net.minecraft.server.network.ServerPlayNetworkHandler handler,
                                           PacketByteBuf buf,
                                           net.fabricmc.fabric.api.networking.v1.PacketSender sender) {
        try {
            UUID playerId = player.getUuid();
            
            ServerNotificationManager manager = ServerNotificationManager.getInstance();
            List<ServerNotification> history = manager.getPlayerNotificationHistory(playerId);
            
            PacketByteBuf responseBuf = PacketByteBufs.create();
            
            // Write number of history notifications
            responseBuf.writeInt(history.size());
            
            // Write each history notification (simplified)
            for (ServerNotification notification : history) {
                responseBuf.writeUuid(notification.getId());
                responseBuf.writeString(notification.getTitle(), NetworkConstants.MAX_STRING_LENGTH);
                responseBuf.writeString(notification.getMessage(), NetworkConstants.MAX_STRING_LENGTH);
                responseBuf.writeInt(notification.getType().ordinal());
                responseBuf.writeLong(notification.getCreatedTime());
                responseBuf.writeBoolean(notification.isRead());
                responseBuf.writeLong(notification.getReadTime());
            }
            
            // Send history (using same sync packet for simplicity)
            NetworkManager.sendToPlayer(player, NOTIFICATION_SYNC, responseBuf);
            
            Pokecobbleclaim.LOGGER.debug("Sent notification history to player " + player.getName().getString() + " (" + history.size() + " notifications)");
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling notification history request: " + e.getMessage());
        }
    }
    
    /**
     * Handles notification sync from server.
     */
    @Environment(EnvType.CLIENT)
    private static void handleNotificationSync(net.minecraft.client.MinecraftClient client,
                                             net.minecraft.client.network.ClientPlayNetworkHandler handler,
                                             PacketByteBuf buf,
                                             net.fabricmc.fabric.api.networking.v1.PacketSender sender) {
        try {
            // Read data version
            int version = buf.readInt();
            
            // Read number of notifications
            int notificationCount = buf.readInt();
            
            // Read each notification
            for (int i = 0; i < notificationCount; i++) {
                UUID id = buf.readUuid();
                UUID playerId = buf.readUuid();
                String title = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                String message = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                ServerNotification.Type type = ServerNotification.Type.values()[buf.readInt()];
                int durationTicks = buf.readInt();
                int remainingTicks = buf.readInt();
                long createdTime = buf.readLong();
                boolean read = buf.readBoolean();
                long readTime = buf.readLong();
                
                // Read optional town data
                String townName = null;
                if (buf.readBoolean()) {
                    townName = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                }
                
                UUID townId = null;
                if (buf.readBoolean()) {
                    townId = buf.readUuid();
                }
                
                // Create notification object
                ServerNotification notification = new ServerNotification(
                    id, playerId, title, message, type, durationTicks, createdTime, townName, townId
                );
                notification.setRemainingTicks(remainingTicks);
                if (read) {
                    notification.markAsRead();
                }
                
                // Update client-side notification manager
                com.pokecobble.notification.ClientNotificationManager.getInstance().updateNotification(notification, version);
            }
            
            // Fire sync event
            com.pokecobble.town.client.ClientSyncManager.getInstance().fireEvent("notifications_updated", notificationCount);
            
            Pokecobbleclaim.LOGGER.debug("Received notification sync: " + notificationCount + " notifications (version " + version + ")");
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling notification sync: " + e.getMessage());
        }
    }
    
    /**
     * Sends mark read request to server.
     */
    @Environment(EnvType.CLIENT)
    public static void sendMarkReadRequest(UUID notificationId) {
        try {
            PacketByteBuf buf = PacketByteBufs.create();
            buf.writeUuid(notificationId);
            
            NetworkManager.sendToServer(NOTIFICATION_MARK_READ, buf);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending mark read request: " + e.getMessage());
        }
    }
    
    /**
     * Sends remove notification request to server.
     */
    @Environment(EnvType.CLIENT)
    public static void sendRemoveRequest(UUID notificationId) {
        try {
            PacketByteBuf buf = PacketByteBufs.create();
            buf.writeUuid(notificationId);
            
            NetworkManager.sendToServer(NOTIFICATION_REMOVE, buf);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending remove request: " + e.getMessage());
        }
    }
    
    /**
     * Requests notification sync from server.
     */
    @Environment(EnvType.CLIENT)
    public static void requestNotificationSync() {
        try {
            PacketByteBuf buf = PacketByteBufs.create();
            
            NetworkManager.sendToServer(NOTIFICATION_REQUEST, buf);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error requesting notification sync: " + e.getMessage());
        }
    }
    
    /**
     * Requests notification history from server.
     */
    @Environment(EnvType.CLIENT)
    public static void requestNotificationHistory() {
        try {
            PacketByteBuf buf = PacketByteBufs.create();
            
            NetworkManager.sendToServer(NOTIFICATION_HISTORY_REQUEST, buf);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error requesting notification history: " + e.getMessage());
        }
    }
}

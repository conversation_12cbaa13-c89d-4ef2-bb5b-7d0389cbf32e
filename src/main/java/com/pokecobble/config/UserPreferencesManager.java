package com.pokecobble.config;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.pokecobble.Pokecobbleclaim;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.loader.api.FabricLoader;

import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Manages user preferences with automatic synchronization between client and server.
 */
@Environment(EnvType.CLIENT)
public class UserPreferencesManager {
    private static UserPreferencesManager instance;
    
    private static final String PREFERENCES_FILE = "pokecobbleclaim-user-preferences.json";
    private static final Gson gson = new GsonBuilder().setPrettyPrinting().create();
    
    // Preference storage
    private final Map<String, Map<String, Object>> preferences = new ConcurrentHashMap<>();
    
    // Change tracking
    private final Set<String> changedCategories = new HashSet<>();
    private long lastSyncTime = 0;
    private static final long SYNC_DELAY_MS = 1000; // 1 second delay for batching changes
    
    private UserPreferencesManager() {
        loadPreferences();
        setupAutoSync();
    }
    
    public static UserPreferencesManager getInstance() {
        if (instance == null) {
            instance = new UserPreferencesManager();
        }
        return instance;
    }
    
    /**
     * Loads preferences from file.
     */
    private void loadPreferences() {
        File prefsFile = new File(FabricLoader.getInstance().getConfigDir().toFile(), PREFERENCES_FILE);
        
        if (!prefsFile.exists()) {
            createDefaultPreferences();
            savePreferences();
            return;
        }
        
        try (FileReader reader = new FileReader(prefsFile)) {
            Map<String, Map<String, Object>> loaded = gson.fromJson(reader, Map.class);
            if (loaded != null) {
                preferences.clear();
                preferences.putAll(loaded);
            }
            
            Pokecobbleclaim.LOGGER.info("Loaded user preferences from: " + PREFERENCES_FILE);
        } catch (IOException e) {
            Pokecobbleclaim.LOGGER.error("Failed to load user preferences", e);
            createDefaultPreferences();
        }
    }
    
    /**
     * Saves preferences to file.
     */
    private void savePreferences() {
        File prefsFile = new File(FabricLoader.getInstance().getConfigDir().toFile(), PREFERENCES_FILE);
        
        try (FileWriter writer = new FileWriter(prefsFile)) {
            gson.toJson(preferences, writer);
            Pokecobbleclaim.LOGGER.debug("Saved user preferences to: " + PREFERENCES_FILE);
        } catch (IOException e) {
            Pokecobbleclaim.LOGGER.error("Failed to save user preferences", e);
        }
    }
    
    /**
     * Creates default preferences.
     */
    private void createDefaultPreferences() {
        // Browser preferences removed
        
        // Performance preferences
        Map<String, Object> performancePrefs = new HashMap<>();
        performancePrefs.put("chunkRenderDistance", 2);
        performancePrefs.put("enableChunkWalls", true);
        performancePrefs.put("chunkTrackerUpdateInterval", 250);
        performancePrefs.put("enablePerformanceMonitoring", false);
        preferences.put(ConfigSynchronizer.CATEGORY_PERFORMANCE, performancePrefs);
        
        // UI preferences
        Map<String, Object> uiPrefs = new HashMap<>();
        uiPrefs.put("showClaimToolHUD", true);
        uiPrefs.put("showChunkBorders", true);
        uiPrefs.put("hudScale", 1.0);
        uiPrefs.put("enableTooltips", true);
        uiPrefs.put("autoRefreshUI", true);
        preferences.put(ConfigSynchronizer.CATEGORY_UI, uiPrefs);
        
        // Phone preferences
        Map<String, Object> phonePrefs = new HashMap<>();
        phonePrefs.put("autoOpenPhone", true);
        phonePrefs.put("notificationSound", true);
        phonePrefs.put("vibration", false);
        phonePrefs.put("notificationDuration", 5000);
        phonePrefs.put("phoneScale", 1.0);
        preferences.put(ConfigSynchronizer.CATEGORY_PHONE, phonePrefs);
        
        // Claim tool preferences
        Map<String, Object> claimToolPrefs = new HashMap<>();
        claimToolPrefs.put("showChunkCoordinates", true);
        claimToolPrefs.put("showOwnerInfo", true);
        claimToolPrefs.put("showPermissionInfo", true);
        claimToolPrefs.put("highlightSelectedChunks", true);
        claimToolPrefs.put("autoSelectMode", false);
        preferences.put(ConfigSynchronizer.CATEGORY_CLAIM_TOOL, claimToolPrefs);

        // NOTE: Town preferences are NOT stored in user preferences because town settings are GLOBAL for the entire town,
        // not per-player. Town settings are managed through TownSettingsManager only.

        Pokecobbleclaim.LOGGER.info("Created default user preferences");
    }
    
    /**
     * Sets up automatic synchronization.
     */
    private void setupAutoSync() {
        // This would ideally use a proper scheduler
        new Thread(() -> {
            while (true) {
                try {
                    Thread.sleep(SYNC_DELAY_MS);
                    
                    if (!changedCategories.isEmpty() && System.currentTimeMillis() - lastSyncTime > SYNC_DELAY_MS) {
                        syncChangedCategories();
                    }
                } catch (InterruptedException e) {
                    break;
                } catch (Exception e) {
                    Pokecobbleclaim.LOGGER.error("Error in auto-sync thread: " + e.getMessage());
                }
            }
        }).start();
    }
    
    /**
     * Syncs changed categories to server.
     */
    private void syncChangedCategories() {
        synchronized (changedCategories) {
            for (String category : changedCategories) {
                Map<String, Object> categoryPrefs = preferences.get(category);
                if (categoryPrefs != null) {
                    ConfigSynchronizer.sendConfigUpdate(category, categoryPrefs);
                }
            }
            changedCategories.clear();
        }
        
        // Save to disk
        savePreferences();
    }
    
    /**
     * Gets a preference value.
     */
    public <T> T getPreference(String category, String key, T defaultValue) {
        Map<String, Object> categoryPrefs = preferences.get(category);
        if (categoryPrefs == null) {
            return defaultValue;
        }
        
        Object value = categoryPrefs.get(key);
        if (value == null) {
            return defaultValue;
        }
        
        try {
            return (T) value;
        } catch (ClassCastException e) {
            Pokecobbleclaim.LOGGER.warn("Type mismatch for preference " + category + "." + key + ": " + e.getMessage());
            return defaultValue;
        }
    }
    
    /**
     * Sets a preference value.
     */
    public void setPreference(String category, String key, Object value) {
        Map<String, Object> categoryPrefs = preferences.computeIfAbsent(category, k -> new HashMap<>());
        
        Object oldValue = categoryPrefs.get(key);
        if (!Objects.equals(oldValue, value)) {
            categoryPrefs.put(key, value);
            
            // Mark category as changed
            synchronized (changedCategories) {
                changedCategories.add(category);
            }
            lastSyncTime = System.currentTimeMillis();
            
            // Apply preference immediately
            applyPreference(category, key, value);
            
            Pokecobbleclaim.LOGGER.debug("Set preference " + category + "." + key + " = " + value);
        }
    }
    
    /**
     * Gets all preferences for a category.
     */
    public Map<String, Object> getCategoryPreferences(String category) {
        Map<String, Object> categoryPrefs = preferences.get(category);
        return categoryPrefs != null ? new HashMap<>(categoryPrefs) : new HashMap<>();
    }
    
    /**
     * Sets all preferences for a category.
     */
    public void setCategoryPreferences(String category, Map<String, Object> categoryPrefs) {
        preferences.put(category, new HashMap<>(categoryPrefs));
        
        // Mark category as changed
        synchronized (changedCategories) {
            changedCategories.add(category);
        }
        lastSyncTime = System.currentTimeMillis();
        
        // Apply all preferences in the category
        for (Map.Entry<String, Object> entry : categoryPrefs.entrySet()) {
            applyPreference(category, entry.getKey(), entry.getValue());
        }
        
        Pokecobbleclaim.LOGGER.debug("Set category preferences for: " + category);
    }
    
    /**
     * Applies a preference change immediately.
     */
    private void applyPreference(String category, String key, Object value) {
        try {
            switch (category) {
                // Browser category removed
                case ConfigSynchronizer.CATEGORY_PERFORMANCE:
                    applyPerformancePreference(key, value);
                    break;
                case ConfigSynchronizer.CATEGORY_UI:
                    applyUIPreference(key, value);
                    break;
                case ConfigSynchronizer.CATEGORY_PHONE:
                    applyPhonePreference(key, value);
                    break;
                case ConfigSynchronizer.CATEGORY_CLAIM_TOOL:
                    applyClaimToolPreference(key, value);
                    break;
                // NOTE: Town preferences are NOT handled here because town settings are GLOBAL for the entire town,
                // not per-player. Town settings are managed through TownSettingsManager only.
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error applying preference " + category + "." + key + ": " + e.getMessage());
        }
    }
    
    // Browser preferences method removed
    
    /**
     * Applies performance preferences.
     */
    private void applyPerformancePreference(String key, Object value) {
        switch (key) {
            case "chunkRenderDistance":
                PerformanceConfig.setChunkRenderDistance(((Double) value).intValue());
                break;
            case "enableChunkWalls":
                PerformanceConfig.setChunkWallsEnabled((Boolean) value);
                break;
            case "chunkTrackerUpdateInterval":
                PerformanceConfig.setChunkTrackerUpdateInterval(((Double) value).intValue());
                break;
            case "enablePerformanceMonitoring":
                PerformanceConfig.setPerformanceMonitoringEnabled((Boolean) value);
                break;
        }
    }
    
    /**
     * Applies UI preferences.
     */
    private void applyUIPreference(String key, Object value) {
        // Apply UI-specific preferences
        Pokecobbleclaim.LOGGER.debug("Applied UI preference: " + key + " = " + value);
    }
    
    /**
     * Applies phone preferences.
     */
    private void applyPhonePreference(String key, Object value) {
        // Apply phone-specific preferences
        Pokecobbleclaim.LOGGER.debug("Applied phone preference: " + key + " = " + value);
    }
    
    /**
     * Applies claim tool preferences.
     */
    private void applyClaimToolPreference(String key, Object value) {
        // Apply claim tool-specific preferences
        Pokecobbleclaim.LOGGER.debug("Applied claim tool preference: " + key + " = " + value);
    }

    // NOTE: Removed applyTownPreference method because town settings are GLOBAL for the entire town,
    // not per-player. Town settings are managed through TownSettingsManager only.
    
    /**
     * Forces immediate sync of all preferences.
     */
    public void forceSyncAll() {
        for (String category : preferences.keySet()) {
            Map<String, Object> categoryPrefs = preferences.get(category);
            if (categoryPrefs != null) {
                ConfigSynchronizer.sendConfigUpdate(category, categoryPrefs);
            }
        }
        
        savePreferences();
        Pokecobbleclaim.LOGGER.debug("Force synced all preferences");
    }
    
    /**
     * Requests sync from server.
     */
    public void requestSyncFromServer() {
        ConfigSynchronizer.requestConfigSync("all");
    }
    
    /**
     * Gets preference statistics.
     */
    public Map<String, Object> getPreferenceStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("categories", preferences.size());
        stats.put("changedCategories", changedCategories.size());
        stats.put("lastSyncTime", lastSyncTime);
        
        // Count total preferences
        int totalPrefs = preferences.values().stream()
                .mapToInt(Map::size)
                .sum();
        stats.put("totalPreferences", totalPrefs);
        
        return stats;
    }
}

package com.pokecobble.config;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.pokecobble.Pokecobbleclaim;

import com.pokecobble.town.network.NetworkConstants;
import com.pokecobble.town.network.NetworkManager;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking;
import net.fabricmc.fabric.api.networking.v1.PacketByteBufs;
import net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.util.Identifier;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Handles synchronization of mod settings, configuration changes, and user preferences
 * between server and client sessions.
 */
public class ConfigSynchronizer {
    
    // Network identifiers
    public static final Identifier CONFIG_SYNC = new Identifier("pokecobbleclaim", "config_sync");
    public static final Identifier CONFIG_UPDATE = new Identifier("pokecobbleclaim", "config_update");
    public static final Identifier CONFIG_REQUEST = new Identifier("pokecobbleclaim", "config_request");
    public static final Identifier USER_PREFERENCES_SYNC = new Identifier("pokecobbleclaim", "user_preferences_sync");
    
    // Server-side configuration storage
    private static final Map<UUID, Map<String, Object>> playerPreferences = new ConcurrentHashMap<>();
    private static final Map<String, Object> serverConfig = new ConcurrentHashMap<>();
    private static final Map<UUID, Integer> playerConfigVersions = new ConcurrentHashMap<>();
    
    // Configuration categories
    public static final String CATEGORY_PERFORMANCE = "performance";
    public static final String CATEGORY_BACKUP = "backup";
    public static final String CATEGORY_UI = "ui";
    public static final String CATEGORY_PHONE = "phone";
    public static final String CATEGORY_CLAIM_TOOL = "claim_tool";
    public static final String CATEGORY_TOWN = "town";
    
    private static final Gson gson = new Gson();
    
    /**
     * Registers server-side packet handlers.
     */
    public static void registerServerHandlers() {
        Pokecobbleclaim.LOGGER.info("Registering ConfigSynchronizer server-side packet handlers");

        // Register config request handler
        ServerPlayNetworking.registerGlobalReceiver(CONFIG_REQUEST, ConfigSynchronizer::handleConfigRequest);
        Pokecobbleclaim.LOGGER.info("Registered CONFIG_REQUEST handler: " + CONFIG_REQUEST);

        // Register config update handler
        ServerPlayNetworking.registerGlobalReceiver(CONFIG_UPDATE, ConfigSynchronizer::handleConfigUpdate);
        Pokecobbleclaim.LOGGER.info("Registered CONFIG_UPDATE handler: " + CONFIG_UPDATE);
    }
    
    /**
     * Registers client-side packet handlers.
     */
    @Environment(EnvType.CLIENT)
    public static void registerClientHandlers() {
        Pokecobbleclaim.LOGGER.info("Registering ConfigSynchronizer client-side packet handlers");

        // Register config sync handler
        ClientPlayNetworking.registerGlobalReceiver(CONFIG_SYNC, ConfigSynchronizer::handleConfigSync);
        Pokecobbleclaim.LOGGER.info("Registered CONFIG_SYNC handler: " + CONFIG_SYNC);

        // Register user preferences sync handler
        ClientPlayNetworking.registerGlobalReceiver(USER_PREFERENCES_SYNC, ConfigSynchronizer::handleUserPreferencesSync);
        Pokecobbleclaim.LOGGER.info("Registered USER_PREFERENCES_SYNC handler: " + USER_PREFERENCES_SYNC);
    }
    
    /**
     * Synchronizes configuration to a player.
     */
    public static void syncConfigToPlayer(MinecraftServer server, UUID playerId) {
        // Check if PlayerManager is available
        if (server.getPlayerManager() == null) {
            Pokecobbleclaim.LOGGER.debug("PlayerManager not available, skipping config sync to player " + playerId);
            return;
        }

        ServerPlayerEntity player = server.getPlayerManager().getPlayer(playerId);
        if (player == null) {
            return;
        }
        
        try {
            PacketByteBuf buf = PacketByteBufs.create();
            
            // Write config version
            int version = playerConfigVersions.getOrDefault(playerId, 0);
            buf.writeInt(version);
            
            // Get player preferences
            Map<String, Object> preferences = playerPreferences.getOrDefault(playerId, new HashMap<>());
            
            // Write number of preference categories
            buf.writeInt(preferences.size());
            
            // Write each preference category
            for (Map.Entry<String, Object> entry : preferences.entrySet()) {
                String category = entry.getKey();
                Object value = entry.getValue();
                
                buf.writeString(category, NetworkConstants.MAX_STRING_LENGTH);
                
                // Serialize value as JSON
                String jsonValue = gson.toJson(value);
                buf.writeString(jsonValue, NetworkConstants.MAX_STRING_LENGTH * 4); // Allow larger JSON
            }
            
            // Send packet
            NetworkManager.sendToPlayer(player, CONFIG_SYNC, buf);
            
            Pokecobbleclaim.LOGGER.debug("Synchronized config to player " + player.getName().getString() + " (" + preferences.size() + " categories)");
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error synchronizing config to player " + playerId + ": " + e.getMessage());
        }
    }
    
    /**
     * Updates player preferences and triggers synchronization.
     */
    public static void updatePlayerPreferences(MinecraftServer server, UUID playerId, String category, Map<String, Object> preferences) {
        try {
            // Update player preferences
            playerPreferences.computeIfAbsent(playerId, k -> new ConcurrentHashMap<>()).put(category, preferences);
            
            // Update version
            playerConfigVersions.put(playerId, playerConfigVersions.getOrDefault(playerId, 0) + 1);
            
            // Sync to player
            syncConfigToPlayer(server, playerId);
            
            Pokecobbleclaim.LOGGER.debug("Updated preferences for player " + playerId + " in category: " + category);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error updating player preferences: " + e.getMessage());
        }
    }
    
    /**
     * Gets player preferences for a specific category.
     */
    public static Map<String, Object> getPlayerPreferences(UUID playerId, String category) {
        Map<String, Object> preferences = playerPreferences.get(playerId);
        if (preferences == null) {
            return new HashMap<>();
        }
        
        Object categoryPrefs = preferences.get(category);
        if (categoryPrefs instanceof Map) {
            return (Map<String, Object>) categoryPrefs;
        }
        
        return new HashMap<>();
    }
    
    /**
     * Handles config request from client.
     */
    private static void handleConfigRequest(MinecraftServer server, ServerPlayerEntity player,
                                          net.minecraft.server.network.ServerPlayNetworkHandler handler,
                                          PacketByteBuf buf,
                                          net.fabricmc.fabric.api.networking.v1.PacketSender sender) {
        try {
            UUID playerId = player.getUuid();
            String requestedCategory = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
            
            if ("all".equals(requestedCategory)) {
                // Sync all config
                syncConfigToPlayer(server, playerId);
            } else {
                // Sync specific category
                syncCategoryToPlayer(server, playerId, requestedCategory);
            }
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling config request: " + e.getMessage());
        }
    }
    
    /**
     * Handles config update from client.
     */
    private static void handleConfigUpdate(MinecraftServer server, ServerPlayerEntity player,
                                         net.minecraft.server.network.ServerPlayNetworkHandler handler,
                                         PacketByteBuf buf,
                                         net.fabricmc.fabric.api.networking.v1.PacketSender sender) {
        try {
            UUID playerId = player.getUuid();
            String category = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
            String jsonData = buf.readString(NetworkConstants.MAX_STRING_LENGTH * 4);

            Pokecobbleclaim.LOGGER.info("SERVER: Received config update packet from player " + player.getName().getString() + " (UUID: " + playerId + ")");
            Pokecobbleclaim.LOGGER.info("SERVER: Config update category: " + category);
            Pokecobbleclaim.LOGGER.info("SERVER: Config update data: " + jsonData);

            // Parse JSON data
            JsonObject jsonObject = gson.fromJson(jsonData, JsonObject.class);
            Map<String, Object> preferences = gson.fromJson(jsonObject, Map.class);

            Pokecobbleclaim.LOGGER.info("SERVER: Parsed preferences: " + preferences);

            // Handle town settings specially
            if (CATEGORY_TOWN.equals(category)) {
                Pokecobbleclaim.LOGGER.info("SERVER: Handling town settings update");
                handleTownSettingsUpdate(server, playerId, preferences);
            } else {
                Pokecobbleclaim.LOGGER.info("SERVER: Handling general config update for category: " + category);
                // Update player preferences for other categories
                updatePlayerPreferences(server, playerId, category, preferences);
            }

            Pokecobbleclaim.LOGGER.info("SERVER: Successfully processed config update from player " + player.getName().getString() + " for category: " + category);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("SERVER: Error handling config update: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Handles town settings update from client.
     */
    private static void handleTownSettingsUpdate(MinecraftServer server, UUID playerId, Map<String, Object> settings) {
        try {
            Pokecobbleclaim.LOGGER.info("SERVER: Processing town settings update for player " + playerId);
            Pokecobbleclaim.LOGGER.info("SERVER: Settings to update: " + settings);

            // Get the player's town
            com.pokecobble.town.Town playerTown = com.pokecobble.town.TownManager.getInstance().getPlayerTown(playerId);
            if (playerTown == null) {
                Pokecobbleclaim.LOGGER.warn("SERVER: Player " + playerId + " tried to update town settings but is not in a town");
                return;
            }

            Pokecobbleclaim.LOGGER.info("SERVER: Player is in town: " + playerTown.getName() + " (ID: " + playerTown.getId() + ")");

            // Update town settings
            com.pokecobble.town.config.TownSettingsManager.setTownSettings(playerTown.getId(), settings);

            // IMPORTANT: Sync ALL town settings to ALL players, not just the individual setting
            // This ensures all clients have the updated settings and the UI shows the correct values
            syncAllTownSettingsToAllPlayers(server);

            Pokecobbleclaim.LOGGER.info("SERVER: Successfully updated town settings for player " + playerId + " in town " + playerTown.getName() + " and synced to all players");

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("SERVER: Error handling town settings update: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Syncs a specific category to a player.
     */
    private static void syncCategoryToPlayer(MinecraftServer server, UUID playerId, String category) {
        // Check if PlayerManager is available
        if (server.getPlayerManager() == null) {
            Pokecobbleclaim.LOGGER.debug("PlayerManager not available, skipping category sync to player " + playerId);
            return;
        }

        ServerPlayerEntity player = server.getPlayerManager().getPlayer(playerId);
        if (player == null) {
            return;
        }

        try {
            PacketByteBuf buf = PacketByteBufs.create();

            // Write config version
            int version = playerConfigVersions.getOrDefault(playerId, 0);
            buf.writeInt(version);

            // Write single category
            buf.writeInt(1); // Number of categories
            buf.writeString(category, NetworkConstants.MAX_STRING_LENGTH);

            // Get category preferences
            Map<String, Object> categoryPrefs = getPlayerPreferences(playerId, category);
            String jsonValue = gson.toJson(categoryPrefs);
            buf.writeString(jsonValue, NetworkConstants.MAX_STRING_LENGTH * 4);

            // Send packet
            NetworkManager.sendToPlayer(player, CONFIG_SYNC, buf);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error syncing category to player: " + e.getMessage());
        }
    }
    
    /**
     * Client-side handlers
     */
    
    @Environment(EnvType.CLIENT)
    private static void handleConfigSync(net.minecraft.client.MinecraftClient client,
                                       net.minecraft.client.network.ClientPlayNetworkHandler handler,
                                       PacketByteBuf buf,
                                       net.fabricmc.fabric.api.networking.v1.PacketSender sender) {
        try {
            // Read config version
            int version = buf.readInt();
            
            // Read number of categories
            int categoryCount = buf.readInt();
            
            // Read each category
            for (int i = 0; i < categoryCount; i++) {
                String category = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                String jsonValue = buf.readString(NetworkConstants.MAX_STRING_LENGTH * 4);
                
                // Parse and apply configuration
                applyClientConfiguration(category, jsonValue);
            }
            
            // Fire config update event
            com.pokecobble.town.client.ClientSyncManager.getInstance().fireEvent("config_updated", Map.of(
                "version", version,
                "categoryCount", categoryCount
            ));
            
            Pokecobbleclaim.LOGGER.debug("Received config sync: " + categoryCount + " categories (version " + version + ")");
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling config sync: " + e.getMessage());
        }
    }
    
    @Environment(EnvType.CLIENT)
    private static void handleUserPreferencesSync(net.minecraft.client.MinecraftClient client,
                                                 net.minecraft.client.network.ClientPlayNetworkHandler handler,
                                                 PacketByteBuf buf,
                                                 net.fabricmc.fabric.api.networking.v1.PacketSender sender) {
        try {
            // Handle user preferences sync
            Pokecobbleclaim.LOGGER.debug("Received user preferences sync");
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling user preferences sync: " + e.getMessage());
        }
    }
    
    /**
     * Applies configuration on the client side.
     */
    @Environment(EnvType.CLIENT)
    private static void applyClientConfiguration(String category, String jsonValue) {
        try {
            Map<String, Object> config = gson.fromJson(jsonValue, Map.class);
            
            switch (category) {
                case CATEGORY_PERFORMANCE:
                    applyPerformanceConfig(config);
                    break;
                case CATEGORY_UI:
                    applyUIConfig(config);
                    break;
                case CATEGORY_PHONE:
                    applyPhoneConfig(config);
                    break;
                case CATEGORY_CLAIM_TOOL:
                    applyClaimToolConfig(config);
                    break;
                case CATEGORY_TOWN:
                    applyTownConfig(config);
                    break;
                default:
                    Pokecobbleclaim.LOGGER.warn("Unknown config category: " + category);
                    break;
            }
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error applying client configuration for category " + category + ": " + e.getMessage());
        }
    }
    

    
    /**
     * Applies performance configuration.
     */
    @Environment(EnvType.CLIENT)
    private static void applyPerformanceConfig(Map<String, Object> config) {
        if (config.containsKey("chunkRenderDistance")) {
            PerformanceConfig.setChunkRenderDistance(((Double) config.get("chunkRenderDistance")).intValue());
        }
        if (config.containsKey("enableChunkWalls")) {
            PerformanceConfig.setChunkWallsEnabled((Boolean) config.get("enableChunkWalls"));
        }
    }
    
    /**
     * Applies UI configuration.
     */
    @Environment(EnvType.CLIENT)
    private static void applyUIConfig(Map<String, Object> config) {
        // Apply UI-specific configuration
        Pokecobbleclaim.LOGGER.debug("Applied UI config: " + config);
    }
    
    /**
     * Applies phone configuration.
     */
    @Environment(EnvType.CLIENT)
    private static void applyPhoneConfig(Map<String, Object> config) {
        // Apply phone-specific configuration
        Pokecobbleclaim.LOGGER.debug("Applied phone config: " + config);
    }
    
    /**
     * Applies claim tool configuration.
     */
    @Environment(EnvType.CLIENT)
    private static void applyClaimToolConfig(Map<String, Object> config) {
        // Apply claim tool-specific configuration
        Pokecobbleclaim.LOGGER.debug("Applied claim tool config: " + config);
    }

    /**
     * Applies town configuration.
     */
    @Environment(EnvType.CLIENT)
    private static void applyTownConfig(Map<String, Object> config) {
        try {
            net.minecraft.client.MinecraftClient client = net.minecraft.client.MinecraftClient.getInstance();
            if (client.player == null) {
                return;
            }

            Pokecobbleclaim.LOGGER.info("CLIENT: Applying town config with " + config.size() + " entries");
            int appliedCount = 0;

            // Apply settings for all towns
            for (Map.Entry<String, Object> entry : config.entrySet()) {
                try {
                    String townIdStr = entry.getKey();
                    UUID townId = UUID.fromString(townIdStr);

                    if (entry.getValue() instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> townSettings = (Map<String, Object>) entry.getValue();

                        // Apply town settings to the client-side town manager
                        com.pokecobble.town.config.TownSettingsManager.setTownSettings(townId, townSettings);
                        appliedCount++;

                        Pokecobbleclaim.LOGGER.info("CLIENT: Applied town settings for " + townId + ": " + townSettings);

                        // Get town name for logging and verify the settings were applied
                        com.pokecobble.town.Town town = com.pokecobble.town.TownManager.getInstance().getTownById(townId);
                        com.pokecobble.town.Town clientTown = null;
                        try {
                            clientTown = com.pokecobble.town.client.ClientTownManager.getInstance().getTown(townId);
                        } catch (Exception e) {
                            // ClientTownManager might not be available
                        }

                        String townName = town != null ? town.getName() : (clientTown != null ? clientTown.getName() : townIdStr);

                        Pokecobbleclaim.LOGGER.debug("Applied settings for town " + townName + " (" + townId + "): " + townSettings);

                        // Verify that the town object was actually updated
                        if (townSettings.containsKey("isOpen")) {
                            boolean expectedOpen = (Boolean) townSettings.get("isOpen");
                            boolean actualOpen = false;
                            if (town != null) {
                                actualOpen = town.getJoinType() == com.pokecobble.town.Town.JoinType.OPEN;
                                Pokecobbleclaim.LOGGER.debug("Town " + townName + " in TownManager: expected isOpen=" + expectedOpen + ", actual=" + actualOpen);
                            }
                            if (clientTown != null) {
                                boolean clientActualOpen = clientTown.getJoinType() == com.pokecobble.town.Town.JoinType.OPEN;
                                Pokecobbleclaim.LOGGER.debug("Town " + townName + " in ClientTownManager: expected isOpen=" + expectedOpen + ", actual=" + clientActualOpen);
                            }
                        }
                    }
                } catch (Exception e) {
                    Pokecobbleclaim.LOGGER.warn("Failed to apply settings for town " + entry.getKey() + ": " + e.getMessage());
                }
            }

            // NOTE: We do NOT store town settings in user preferences because town settings are GLOBAL for the entire town,
            // not per-player. Town settings are stored in TownSettingsManager and applied to town objects directly.

            // Force refresh of UI components that depend on town data
            try {
                com.pokecobble.ui.UIDataRefreshManager.getInstance().forceRefreshAll();
                Pokecobbleclaim.LOGGER.debug("Refreshed UI components after town settings sync");
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.warn("Failed to refresh UI components: " + e.getMessage());
            }

            Pokecobbleclaim.LOGGER.info("Applied town settings for " + appliedCount + " towns to client");

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error applying town config: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Sends configuration update to server.
     */
    @Environment(EnvType.CLIENT)
    public static void sendConfigUpdate(String category, Map<String, Object> config) {
        try {
            PacketByteBuf buf = PacketByteBufs.create();
            buf.writeString(category, NetworkConstants.MAX_STRING_LENGTH);

            String jsonData = gson.toJson(config);
            buf.writeString(jsonData, NetworkConstants.MAX_STRING_LENGTH * 4);

            Pokecobbleclaim.LOGGER.info("Sending config update to server - Category: " + category + ", Data: " + jsonData);

            NetworkManager.sendToServer(CONFIG_UPDATE, buf);

            Pokecobbleclaim.LOGGER.debug("Config update packet sent successfully");

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending config update: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Requests configuration sync from server.
     */
    @Environment(EnvType.CLIENT)
    public static void requestConfigSync(String category) {
        try {
            PacketByteBuf buf = PacketByteBufs.create();
            buf.writeString(category, NetworkConstants.MAX_STRING_LENGTH);
            
            NetworkManager.sendToServer(CONFIG_REQUEST, buf);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error requesting config sync: " + e.getMessage());
        }
    }
    
    /**
     * Syncs town settings to a player when they join a town.
     */
    public static void syncTownSettingsToPlayer(MinecraftServer server, UUID playerId) {
        try {
            // Get all towns and their settings
            java.util.Collection<com.pokecobble.town.Town> allTowns = com.pokecobble.town.TownManager.getInstance().getAllTowns();
            java.util.Map<String, Object> allTownSettings = new java.util.HashMap<>();

            // Collect settings for all towns
            for (com.pokecobble.town.Town town : allTowns) {
                // First check if there are existing custom settings
                java.util.Map<String, Object> existingSettings = com.pokecobble.town.config.TownSettingsManager.getExistingTownSettings(town.getId());
                java.util.Map<String, Object> townSettings = com.pokecobble.town.config.TownSettingsManager.getTownSettings(town.getId());

                // Store settings with town ID as key for client-side lookup
                allTownSettings.put(town.getId().toString(), townSettings);

                if (existingSettings != null && !existingSettings.isEmpty()) {
                    Pokecobbleclaim.LOGGER.debug("Collected CUSTOM settings for town " + town.getName() + " (" + town.getId() + "): " + townSettings);
                } else {
                    Pokecobbleclaim.LOGGER.debug("Collected DEFAULT settings for town " + town.getName() + " (" + town.getId() + "): " + townSettings);
                }
            }

            Pokecobbleclaim.LOGGER.info("Syncing settings for " + allTowns.size() + " towns to player " + playerId);

            // Update player preferences with all town settings
            updatePlayerPreferences(server, playerId, CATEGORY_TOWN, allTownSettings);

            Pokecobbleclaim.LOGGER.info("Successfully synced all town settings to player " + playerId);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error syncing town settings to player: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Syncs all town settings to all online players.
     * Call this when any town setting changes to keep all clients updated.
     */
    public static void syncAllTownSettingsToAllPlayers(MinecraftServer server) {
        try {
            // Check if PlayerManager is available
            if (server.getPlayerManager() == null) {
                Pokecobbleclaim.LOGGER.debug("PlayerManager not available, skipping town settings sync to all players");
                return;
            }

            java.util.Collection<net.minecraft.server.network.ServerPlayerEntity> players = server.getPlayerManager().getPlayerList();

            for (net.minecraft.server.network.ServerPlayerEntity player : players) {
                syncTownSettingsToPlayer(server, player.getUuid());
            }

            Pokecobbleclaim.LOGGER.info("Synced all town settings to " + players.size() + " online players");

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error syncing town settings to all players: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Clears player configuration data (called on disconnect).
     */
    public static void clearPlayerConfig(UUID playerId) {
        playerPreferences.remove(playerId);
        playerConfigVersions.remove(playerId);
    }
}

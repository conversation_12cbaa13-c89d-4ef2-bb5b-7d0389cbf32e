package com.pokecobble.status;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.election.Election;
import com.pokecobble.town.election.ElectionManager;
import com.pokecobble.town.network.NetworkConstants;
import com.pokecobble.town.network.NetworkManager;
import net.fabricmc.fabric.api.networking.v1.PacketByteBufs;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.util.Identifier;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Manages real-time status updates for dynamic information like online players,
 * town activity, election progress, and other live data.
 */
public class RealTimeStatusManager {
    private static RealTimeStatusManager instance;
    
    // Network identifiers
    public static final Identifier STATUS_UPDATE = new Identifier("pokecobbleclaim", "status_update");
    public static final Identifier STATUS_SUBSCRIPTION = new Identifier("pokecobbleclaim", "status_subscription");
    
    // Status tracking
    private final Map<String, StatusData> currentStatus = new ConcurrentHashMap<>();
    private final Map<UUID, Set<String>> playerSubscriptions = new ConcurrentHashMap<>();
    private final Map<String, Set<UUID>> statusSubscribers = new ConcurrentHashMap<>();
    
    // Update scheduling
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);
    private MinecraftServer server;
    
    // Configuration
    private static final long FAST_UPDATE_INTERVAL_MS = 1000; // 1 second for critical updates
    private static final long NORMAL_UPDATE_INTERVAL_MS = 5000; // 5 seconds for normal updates
    private static final long SLOW_UPDATE_INTERVAL_MS = 30000; // 30 seconds for slow updates
    
    // Status types
    public static final String STATUS_ONLINE_PLAYERS = "online_players";
    public static final String STATUS_TOWN_ACTIVITY = "town_activity";
    public static final String STATUS_ELECTION_PROGRESS = "election_progress";
    public static final String STATUS_SERVER_STATS = "server_stats";
    public static final String STATUS_TOWN_STATS = "town_stats";
    public static final String STATUS_PLAYER_ACTIVITY = "player_activity";
    
    private RealTimeStatusManager() {}
    
    public static RealTimeStatusManager getInstance() {
        if (instance == null) {
            instance = new RealTimeStatusManager();
        }
        return instance;
    }
    
    /**
     * Initializes the status manager with the server instance.
     */
    public void initialize(MinecraftServer server) {
        this.server = server;
        startStatusUpdates();
        Pokecobbleclaim.LOGGER.info("Real-time status manager initialized");
    }
    
    /**
     * Starts periodic status updates.
     */
    private void startStatusUpdates() {
        // Fast updates (1 second) - critical real-time data
        scheduler.scheduleAtFixedRate(this::updateFastStatus, 0, FAST_UPDATE_INTERVAL_MS, TimeUnit.MILLISECONDS);
        
        // Normal updates (5 seconds) - regular status data
        scheduler.scheduleAtFixedRate(this::updateNormalStatus, 0, NORMAL_UPDATE_INTERVAL_MS, TimeUnit.MILLISECONDS);
        
        // Slow updates (30 seconds) - statistical data
        scheduler.scheduleAtFixedRate(this::updateSlowStatus, 0, SLOW_UPDATE_INTERVAL_MS, TimeUnit.MILLISECONDS);
    }
    
    /**
     * Updates fast-changing status data.
     */
    private void updateFastStatus() {
        if (server == null) return;
        
        try {
            updateOnlinePlayersStatus();
            updateElectionProgressStatus();
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error updating fast status: " + e.getMessage());
        }
    }
    
    /**
     * Updates normal-changing status data.
     */
    private void updateNormalStatus() {
        if (server == null) return;
        
        try {
            updateTownActivityStatus();
            updatePlayerActivityStatus();
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error updating normal status: " + e.getMessage());
        }
    }
    
    /**
     * Updates slow-changing status data.
     */
    private void updateSlowStatus() {
        if (server == null) return;
        
        try {
            updateServerStatsStatus();
            updateTownStatsStatus();
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error updating slow status: " + e.getMessage());
        }
    }
    
    /**
     * Updates online players status.
     */
    private void updateOnlinePlayersStatus() {
        if (server.getPlayerManager() == null) {
            return; // Server not fully initialized yet
        }

        List<ServerPlayerEntity> players = server.getPlayerManager().getPlayerList();
        
        Map<String, Object> statusData = new HashMap<>();
        statusData.put("count", players.size());
        statusData.put("maxPlayers", server.getMaxPlayerCount());
        
        List<Map<String, Object>> playerList = new ArrayList<>();
        for (ServerPlayerEntity player : players) {
            Map<String, Object> playerData = new HashMap<>();
            playerData.put("name", player.getName().getString());
            playerData.put("uuid", player.getUuid().toString());
            
            // Add town information
            Town playerTown = TownManager.getInstance().getPlayerTown(player.getUuid());
            if (playerTown != null) {
                playerData.put("townName", playerTown.getName());
                playerData.put("townId", playerTown.getId().toString());
            }
            
            playerList.add(playerData);
        }
        statusData.put("players", playerList);
        statusData.put("timestamp", System.currentTimeMillis());
        
        updateStatus(STATUS_ONLINE_PLAYERS, statusData);
    }
    
    /**
     * Updates town activity status.
     */
    private void updateTownActivityStatus() {
        if (server.getPlayerManager() == null) {
            return; // Server not fully initialized yet
        }

        Map<String, Object> statusData = new HashMap<>();
        
        Collection<Town> towns = TownManager.getInstance().getAllTowns();
        List<Map<String, Object>> townActivity = new ArrayList<>();
        
        for (Town town : towns) {
            Map<String, Object> townData = new HashMap<>();
            townData.put("name", town.getName());
            townData.put("id", town.getId().toString());
            townData.put("playerCount", town.getPlayerCount());
            townData.put("claimCount", town.getClaimCount());
            townData.put("maxClaims", town.getMaxClaims());
            
            // Count online players in town
            int onlineCount = 0;
            for (UUID playerId : town.getPlayerIds()) {
                if (server.getPlayerManager().getPlayer(playerId) != null) {
                    onlineCount++;
                }
            }
            townData.put("onlineCount", onlineCount);
            
            // Activity level based on online players
            String activityLevel = "inactive";
            if (onlineCount > 0) {
                if (onlineCount >= town.getPlayerCount() * 0.5) {
                    activityLevel = "high";
                } else if (onlineCount >= town.getPlayerCount() * 0.25) {
                    activityLevel = "medium";
                } else {
                    activityLevel = "low";
                }
            }
            townData.put("activityLevel", activityLevel);
            
            townActivity.add(townData);
        }
        
        statusData.put("towns", townActivity);
        statusData.put("totalTowns", towns.size());
        statusData.put("timestamp", System.currentTimeMillis());
        
        updateStatus(STATUS_TOWN_ACTIVITY, statusData);
    }
    
    /**
     * Updates election progress status.
     */
    private void updateElectionProgressStatus() {
        Map<String, Object> statusData = new HashMap<>();
        
        Collection<Election> elections = ElectionManager.getInstance().getAllElections();
        List<Map<String, Object>> electionList = new ArrayList<>();
        
        for (Election election : elections) {
            if (election.isActive()) {
                Map<String, Object> electionData = new HashMap<>();
                electionData.put("townId", election.getTownId().toString());
                electionData.put("type", election.getType().name());
                electionData.put("timeRemaining", election.getTimeRemaining());
                electionData.put("totalVotes", election.getTotalVotes());
                electionData.put("eligibleVoters", election.getEligibleVoters().size());
                electionData.put("turnoutPercentage", election.getTurnoutPercentage());
                
                // Get town name
                Town town = TownManager.getInstance().getTownById(election.getTownId());
                if (town != null) {
                    electionData.put("townName", town.getName());
                }
                
                electionList.add(electionData);
            }
        }
        
        statusData.put("activeElections", electionList);
        statusData.put("totalActiveElections", electionList.size());
        statusData.put("timestamp", System.currentTimeMillis());
        
        updateStatus(STATUS_ELECTION_PROGRESS, statusData);
    }
    
    /**
     * Updates server statistics status.
     */
    private void updateServerStatsStatus() {
        Map<String, Object> statusData = new HashMap<>();
        
        // Server performance metrics
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        
        statusData.put("memoryUsed", usedMemory);
        statusData.put("memoryTotal", totalMemory);
        statusData.put("memoryFree", freeMemory);
        statusData.put("memoryUsagePercent", (double) usedMemory / totalMemory * 100);
        
        // Server uptime
        statusData.put("uptime", System.currentTimeMillis() - server.getTimeReference());
        
        // TPS (if available)
        statusData.put("tps", server.getTickTime());
        
        statusData.put("timestamp", System.currentTimeMillis());
        
        updateStatus(STATUS_SERVER_STATS, statusData);
    }
    
    /**
     * Updates town statistics status.
     */
    private void updateTownStatsStatus() {
        if (server.getPlayerManager() == null) {
            return; // Server not fully initialized yet
        }

        Map<String, Object> statusData = new HashMap<>();
        
        Collection<Town> towns = TownManager.getInstance().getAllTowns();
        
        int totalPlayers = 0;
        int totalClaims = 0;
        int activeTowns = 0;
        
        for (Town town : towns) {
            totalPlayers += town.getPlayerCount();
            totalClaims += town.getClaimCount();
            
            // Check if town has online players
            boolean hasOnlinePlayers = false;
            for (UUID playerId : town.getPlayerIds()) {
                if (server.getPlayerManager().getPlayer(playerId) != null) {
                    hasOnlinePlayers = true;
                    break;
                }
            }
            if (hasOnlinePlayers) {
                activeTowns++;
            }
        }
        
        statusData.put("totalTowns", towns.size());
        statusData.put("activeTowns", activeTowns);
        statusData.put("totalPlayers", totalPlayers);
        statusData.put("totalClaims", totalClaims);
        statusData.put("averagePlayersPerTown", towns.isEmpty() ? 0 : (double) totalPlayers / towns.size());
        statusData.put("averageClaimsPerTown", towns.isEmpty() ? 0 : (double) totalClaims / towns.size());
        statusData.put("timestamp", System.currentTimeMillis());
        
        updateStatus(STATUS_TOWN_STATS, statusData);
    }
    
    /**
     * Updates player activity status.
     */
    private void updatePlayerActivityStatus() {
        if (server.getPlayerManager() == null) {
            return; // Server not fully initialized yet
        }

        Map<String, Object> statusData = new HashMap<>();

        List<ServerPlayerEntity> players = server.getPlayerManager().getPlayerList();
        Map<String, Integer> townPlayerCounts = new HashMap<>();
        
        for (ServerPlayerEntity player : players) {
            Town playerTown = TownManager.getInstance().getPlayerTown(player.getUuid());
            if (playerTown != null) {
                townPlayerCounts.put(playerTown.getName(), 
                    townPlayerCounts.getOrDefault(playerTown.getName(), 0) + 1);
            }
        }
        
        statusData.put("onlinePlayersByTown", townPlayerCounts);
        statusData.put("playersWithoutTown", players.size() - townPlayerCounts.values().stream().mapToInt(Integer::intValue).sum());
        statusData.put("timestamp", System.currentTimeMillis());
        
        updateStatus(STATUS_PLAYER_ACTIVITY, statusData);
    }
    
    /**
     * Updates a status and notifies subscribers.
     */
    private void updateStatus(String statusType, Map<String, Object> data) {
        StatusData statusData = new StatusData(statusType, data, System.currentTimeMillis());
        currentStatus.put(statusType, statusData);
        
        // Notify subscribers
        Set<UUID> subscribers = statusSubscribers.get(statusType);
        if (subscribers != null && !subscribers.isEmpty()) {
            sendStatusUpdate(statusType, statusData, subscribers);
        }
    }
    
    /**
     * Sends status update to subscribers.
     */
    private void sendStatusUpdate(String statusType, StatusData statusData, Set<UUID> subscribers) {
        try {
            PacketByteBuf buf = PacketByteBufs.create();
            buf.writeString(statusType, NetworkConstants.MAX_STRING_LENGTH);
            buf.writeLong(statusData.timestamp);
            
            // Serialize data as JSON
            String jsonData = com.google.gson.JsonParser.parseString(
                new com.google.gson.Gson().toJson(statusData.data)
            ).toString();
            buf.writeString(jsonData, NetworkConstants.MAX_STRING_LENGTH * 8); // Allow large JSON
            
            // Send to all subscribers
            if (server.getPlayerManager() != null) {
                for (UUID playerId : subscribers) {
                    ServerPlayerEntity player = server.getPlayerManager().getPlayer(playerId);
                    if (player != null) {
                        NetworkManager.sendToPlayer(player, STATUS_UPDATE, buf);
                    }
                }
            }
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending status update: " + e.getMessage());
        }
    }
    
    /**
     * Subscribes a player to status updates.
     */
    public void subscribePlayer(UUID playerId, String statusType) {
        playerSubscriptions.computeIfAbsent(playerId, k -> new HashSet<>()).add(statusType);
        statusSubscribers.computeIfAbsent(statusType, k -> new HashSet<>()).add(playerId);
        
        // Send current status immediately
        StatusData currentData = currentStatus.get(statusType);
        if (currentData != null) {
            sendStatusUpdate(statusType, currentData, Set.of(playerId));
        }
        
        Pokecobbleclaim.LOGGER.debug("Player " + playerId + " subscribed to status: " + statusType);
    }
    
    /**
     * Unsubscribes a player from status updates.
     */
    public void unsubscribePlayer(UUID playerId, String statusType) {
        Set<String> playerSubs = playerSubscriptions.get(playerId);
        if (playerSubs != null) {
            playerSubs.remove(statusType);
            if (playerSubs.isEmpty()) {
                playerSubscriptions.remove(playerId);
            }
        }
        
        Set<UUID> statusSubs = statusSubscribers.get(statusType);
        if (statusSubs != null) {
            statusSubs.remove(playerId);
            if (statusSubs.isEmpty()) {
                statusSubscribers.remove(statusType);
            }
        }
        
        Pokecobbleclaim.LOGGER.debug("Player " + playerId + " unsubscribed from status: " + statusType);
    }
    
    /**
     * Clears all subscriptions for a player (called on disconnect).
     */
    public void clearPlayerSubscriptions(UUID playerId) {
        Set<String> subscriptions = playerSubscriptions.remove(playerId);
        if (subscriptions != null) {
            for (String statusType : subscriptions) {
                Set<UUID> statusSubs = statusSubscribers.get(statusType);
                if (statusSubs != null) {
                    statusSubs.remove(playerId);
                    if (statusSubs.isEmpty()) {
                        statusSubscribers.remove(statusType);
                    }
                }
            }
        }
    }
    
    /**
     * Gets current status data.
     */
    public StatusData getCurrentStatus(String statusType) {
        return currentStatus.get(statusType);
    }
    
    /**
     * Registers server-side packet handlers.
     */
    public static void registerServerHandlers() {
        net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking.registerGlobalReceiver(
            STATUS_SUBSCRIPTION, RealTimeStatusManager::handleStatusSubscription);
    }

    /**
     * Handles status subscription from client.
     */
    private static void handleStatusSubscription(net.minecraft.server.MinecraftServer server,
                                                net.minecraft.server.network.ServerPlayerEntity player,
                                                net.minecraft.server.network.ServerPlayNetworkHandler handler,
                                                net.minecraft.network.PacketByteBuf buf,
                                                net.fabricmc.fabric.api.networking.v1.PacketSender sender) {
        try {
            String statusType = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
            boolean subscribe = buf.readBoolean();
            UUID playerId = player.getUuid();

            RealTimeStatusManager instance = getInstance();

            if (subscribe) {
                instance.subscribePlayer(playerId, statusType);
            } else {
                instance.unsubscribePlayer(playerId, statusType);
            }

            Pokecobbleclaim.LOGGER.debug("Player " + player.getName().getString() +
                                       (subscribe ? " subscribed to " : " unsubscribed from ") +
                                       "status: " + statusType);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling status subscription: " + e.getMessage());
        }
    }

    /**
     * Shuts down the status manager.
     */
    public void shutdown() {
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
        }
    }
    
    /**
     * Represents status data with timestamp.
     */
    public static class StatusData {
        public final String type;
        public final Map<String, Object> data;
        public final long timestamp;
        
        public StatusData(String type, Map<String, Object> data, long timestamp) {
            this.type = type;
            this.data = data;
            this.timestamp = timestamp;
        }
    }
}

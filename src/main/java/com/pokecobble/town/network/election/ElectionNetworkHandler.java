package com.pokecobble.town.network.election;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.TownPlayer;
import com.pokecobble.town.election.Election;
import com.pokecobble.town.election.ElectionManager;
import com.pokecobble.town.gui.NotificationManager;
import com.pokecobble.town.network.NetworkConstants;
import com.pokecobble.town.network.NetworkManager;
import com.pokecobble.town.network.PacketValidator;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking;
import net.fabricmc.fabric.api.networking.v1.PacketSender;
import net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.network.ClientPlayNetworkHandler;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayNetworkHandler;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Handles network communication for election-related operations.
 */
public class ElectionNetworkHandler {

    // Callback interface for election updates
    public interface ElectionUpdateCallback {
        void onElectionUpdate(UUID townId);
    }

    // List of registered callbacks
    private static final List<ElectionUpdateCallback> electionUpdateCallbacks = new ArrayList<>();

    /**
     * Registers a callback to be notified when election data is updated.
     *
     * @param callback The callback to register
     */
    public static void registerElectionUpdateCallback(ElectionUpdateCallback callback) {
        electionUpdateCallbacks.add(callback);
    }

    /**
     * Unregisters a previously registered callback.
     *
     * @param callback The callback to unregister
     */
    public static void unregisterElectionUpdateCallback(ElectionUpdateCallback callback) {
        electionUpdateCallbacks.remove(callback);
    }

    /**
     * Notifies all registered callbacks that an election has been updated.
     *
     * @param townId The ID of the town whose election was updated
     */
    public static void notifyElectionUpdated(UUID townId) {
        for (ElectionUpdateCallback callback : electionUpdateCallbacks) {
            callback.onElectionUpdate(townId);
        }
    }

    /**
     * Registers client-side packet handlers.
     */
    @Environment(EnvType.CLIENT)
    public static void registerClientHandlers() {
        // Register election data response handler
        ClientPlayNetworking.registerGlobalReceiver(
                NetworkConstants.ELECTION_DATA_RESPONSE,
                ElectionNetworkHandler::handleElectionDataResponse
        );

        // Register election vote response handler
        ClientPlayNetworking.registerGlobalReceiver(
                NetworkConstants.ELECTION_VOTE_RESPONSE,
                ElectionNetworkHandler::handleElectionVoteResponse
        );
    }

    /**
     * Registers server-side packet handlers.
     */
    public static void registerServerHandlers() {
        // Register election data request handler
        ServerPlayNetworking.registerGlobalReceiver(
                NetworkConstants.ELECTION_DATA_REQUEST,
                ElectionNetworkHandler::handleElectionDataRequest
        );

        // Register election vote request handler
        ServerPlayNetworking.registerGlobalReceiver(
                NetworkConstants.ELECTION_VOTE_REQUEST,
                ElectionNetworkHandler::handleElectionVoteRequest
        );
    }

    // Client-side methods

    /**
     * Requests election data from the server.
     *
     * @param townId The UUID of the town to request election data for
     */
    @Environment(EnvType.CLIENT)
    public static void requestElectionData(UUID townId) {
        try {
            // Validate UUID
            PacketValidator.validateUUID(townId);

            // Create packet buffer
            PacketByteBuf buf = NetworkManager.createPacket();

            // Add player UUID for validation
            if (MinecraftClient.getInstance().player != null) {
                buf.writeUuid(MinecraftClient.getInstance().player.getUuid());
            } else {
                return; // Can't send without a player
            }

            // Add town UUID
            buf.writeUuid(townId);

            // Send packet to server
            NetworkManager.sendToServer(NetworkConstants.ELECTION_DATA_REQUEST, buf);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error requesting election data: " + e.getMessage());
        }
    }

    /**
     * Sends a vote for a player in an election.
     *
     * @param townId The UUID of the town
     * @param candidateId The UUID of the candidate to vote for
     */
    @Environment(EnvType.CLIENT)
    public static void voteForCandidate(UUID townId, UUID candidateId) {
        try {
            // Validate UUIDs
            PacketValidator.validateUUID(townId);
            PacketValidator.validateUUID(candidateId);

            // Create packet buffer
            PacketByteBuf buf = NetworkManager.createPacket();

            // Add player UUID for validation
            if (MinecraftClient.getInstance().player != null) {
                buf.writeUuid(MinecraftClient.getInstance().player.getUuid());
            } else {
                return; // Can't send without a player
            }

            // Add town UUID
            buf.writeUuid(townId);

            // Add candidate UUID
            buf.writeUuid(candidateId);

            // Send packet to server
            NetworkManager.sendToServer(NetworkConstants.ELECTION_VOTE_REQUEST, buf);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error voting for candidate: " + e.getMessage());
        }
    }

    // Client-side packet handlers

    /**
     * Handles election data response packets.
     */
    @Environment(EnvType.CLIENT)
    private static void handleElectionDataResponse(MinecraftClient client, ClientPlayNetworkHandler handler,
                                                 PacketByteBuf buf, PacketSender sender) {
        try {
            // Validate packet size
            PacketValidator.validatePacketSize(buf);

            // Read town UUID
            UUID townId = buf.readUuid();

            // Read if there's an active election
            boolean hasElection = buf.readBoolean();

            // Read data version
            int dataVersion = buf.readInt();

            if (hasElection) {
                // Read election end time
                long endTime = buf.readLong();

                // Read candidate count
                int candidateCount = buf.readInt();
                PacketValidator.validateListSize(candidateCount);

                // Read candidate data
                Map<UUID, Integer> candidates = new HashMap<>();

                for (int i = 0; i < candidateCount; i++) {
                    UUID candidateId = buf.readUuid();
                    int votes = buf.readInt();
                    candidates.put(candidateId, votes);
                }

                // Read if the player has voted
                boolean hasVoted = buf.readBoolean();

                // Read the player's vote if they have voted
                UUID votedFor = hasVoted ? buf.readUuid() : null;

                // Update election data in ElectionManager
                Town town = TownManager.getInstance().getTownById(townId);

                if (town != null) {
                    Election election = new Election(town, endTime);

                    // Add candidates
                    for (Map.Entry<UUID, Integer> entry : candidates.entrySet()) {
                        election.addCandidate(entry.getKey(), entry.getValue());
                    }

                    // Set player's vote
                    if (client.player != null && hasVoted) {
                        election.setPlayerVote(client.player.getUuid(), votedFor);
                    }

                    // Update the election in the client-side ElectionManager (for compatibility)
                    // Note: This is kept for backward compatibility, but ClientElectionManager is the primary cache
                    ElectionManager.getInstance().setElection(town, election);

                    // Update client cache
                    com.pokecobble.town.client.ClientElectionManager.getInstance().updateElection(townId, election, dataVersion);
                    if (hasVoted && client.player != null) {
                        com.pokecobble.town.client.ClientElectionManager.getInstance().updatePlayerVote(townId, votedFor, dataVersion);
                    }

                    // Notify the player
                    NotificationManager.getInstance().addInfoNotification("Election data updated: " + candidateCount + " candidates");

                    // Notify callbacks
                    notifyElectionUpdated(townId);
                }
            } else {
                // No active election
                Town town = TownManager.getInstance().getTownById(townId);

                if (town != null) {
                    ElectionManager.getInstance().removeElection(town);

                    // Update client cache
                    com.pokecobble.town.client.ClientElectionManager.getInstance().updateElection(townId, null, dataVersion);

                    // Notify the player
                    NotificationManager.getInstance().addInfoNotification("No active election for this town");

                    // Notify callbacks
                    notifyElectionUpdated(townId);
                }
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling election data response: " + e.getMessage());
        }
    }

    /**
     * Handles election vote response packets.
     */
    @Environment(EnvType.CLIENT)
    private static void handleElectionVoteResponse(MinecraftClient client, ClientPlayNetworkHandler handler,
                                                 PacketByteBuf buf, PacketSender sender) {
        try {
            // Validate packet size
            PacketValidator.validatePacketSize(buf);

            // Read town UUID
            UUID townId = buf.readUuid();

            // Read if the vote was successful
            boolean success = buf.readBoolean();

            // Read candidate UUID
            UUID candidateId = buf.readUuid();

            // Read candidate name
            String candidateName = buf.readString(NetworkConstants.MAX_STRING_LENGTH);

            // Update election data in ElectionManager
            Town town = TownManager.getInstance().getTownById(townId);

            if (town != null && success) {
                Election election = com.pokecobble.town.client.ClientElectionManager.getInstance().getElection(town.getId());

                if (election != null && client.player != null) {
                    // Update the player's vote
                    election.setPlayerVote(client.player.getUuid(), candidateId);

                    // Increment the candidate's vote count
                    election.addVote(candidateId);

                    // Notify the player
                    NotificationManager.getInstance().addSuccessNotification("Vote cast for " + candidateName);

                    // Notify callbacks
                    notifyElectionUpdated(townId);
                }
            } else {
                // Notify the player of failure
                NotificationManager.getInstance().addErrorNotification("Failed to cast vote for " + candidateName);
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling election vote response: " + e.getMessage());
        }
    }

    // Server-side packet handlers

    /**
     * Handles election data request packets.
     */
    private static void handleElectionDataRequest(MinecraftServer server, ServerPlayerEntity player,
                                                ServerPlayNetworkHandler handler, PacketByteBuf buf,
                                                PacketSender sender) {
        try {
            // Validate packet size
            PacketValidator.validatePacketSize(buf);

            // Read player UUID for validation
            UUID requestingPlayerId = buf.readUuid();

            // Validate that the requesting player is the same as the sender
            if (!player.getUuid().equals(requestingPlayerId)) {
                Pokecobbleclaim.LOGGER.warn("Player " + player.getName().getString() + " tried to request election data for another player");
                return;
            }

            // Read town UUID
            UUID townId = buf.readUuid();

            // Get the town
            Town town = TownManager.getInstance().getTownById(townId);

            if (town == null) {
                // Town not found
                player.sendMessage(Text.literal("Town not found").formatted(Formatting.RED), false);
                return;
            }

            // Check if the player is in the town
            if (!town.getPlayers().contains(player.getUuid())) {
                // Player is not in the town
                player.sendMessage(Text.literal("You are not in this town").formatted(Formatting.RED), false);
                return;
            }

            // Get the election
            Election election = ElectionManager.getInstance().getElection(town);

            // Create response packet
            PacketByteBuf responseBuf = NetworkManager.createPacket();

            // Write town UUID
            responseBuf.writeUuid(townId);

            if (election != null) {
                // There's an active election
                responseBuf.writeBoolean(true);

                // Write election end time
                responseBuf.writeLong(election.getEndTime());

                // Get candidates and their vote counts
                List<UUID> candidateList = election.getCandidates();
                Map<UUID, Integer> voteCount = election.getVoteCount();

                // Write candidate count
                responseBuf.writeInt(candidateList.size());

                // Write candidate data
                for (UUID candidateId : candidateList) {
                    responseBuf.writeUuid(candidateId);
                    responseBuf.writeInt(voteCount.getOrDefault(candidateId, 0));
                }

                // Write if the player has voted
                boolean hasVoted = election.hasVoted(player.getUuid());
                responseBuf.writeBoolean(hasVoted);

                // Write the player's vote if they have voted
                if (hasVoted) {
                    responseBuf.writeUuid(election.getPlayerVote(player.getUuid()));
                }
            } else {
                // No active election
                responseBuf.writeBoolean(false);
            }

            // Send response to player
            NetworkManager.sendToPlayer(player, NetworkConstants.ELECTION_DATA_RESPONSE, responseBuf);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling election data request: " + e.getMessage());
        }
    }

    /**
     * Handles election vote request packets.
     */
    private static void handleElectionVoteRequest(MinecraftServer server, ServerPlayerEntity player,
                                                ServerPlayNetworkHandler handler, PacketByteBuf buf,
                                                PacketSender sender) {
        try {
            // Validate packet size
            PacketValidator.validatePacketSize(buf);

            // Read player UUID for validation
            UUID requestingPlayerId = buf.readUuid();

            // Validate that the requesting player is the same as the sender
            if (!player.getUuid().equals(requestingPlayerId)) {
                Pokecobbleclaim.LOGGER.warn("Player " + player.getName().getString() + " tried to vote for another player");
                return;
            }

            // Read town UUID
            UUID townId = buf.readUuid();

            // Read candidate UUID
            UUID candidateId = buf.readUuid();

            // Get the town
            Town town = TownManager.getInstance().getTownById(townId);

            if (town == null) {
                // Town not found
                player.sendMessage(Text.literal("Town not found").formatted(Formatting.RED), false);
                return;
            }

            // Check if the player is in the town
            if (!town.getPlayers().contains(player.getUuid())) {
                // Player is not in the town
                player.sendMessage(Text.literal("You are not in this town").formatted(Formatting.RED), false);
                return;
            }

            // Get the election
            Election election = ElectionManager.getInstance().getElection(town);

            if (election == null) {
                // No active election
                player.sendMessage(Text.literal("There is no active election").formatted(Formatting.RED), false);
                return;
            }

            // Check if the player has already voted
            if (election.hasVoted(player.getUuid())) {
                // Player has already voted
                player.sendMessage(Text.literal("You have already voted").formatted(Formatting.RED), false);
                return;
            }

            // Check if the candidate is in the town
            if (!town.getPlayers().contains(candidateId)) {
                // Candidate is not in the town
                player.sendMessage(Text.literal("Candidate is not in this town").formatted(Formatting.RED), false);
                return;
            }

            // Check if the candidate is a valid candidate
            if (!election.isCandidate(candidateId)) {
                // Candidate is not valid
                player.sendMessage(Text.literal("Invalid candidate").formatted(Formatting.RED), false);
                return;
            }

            // Record the vote
            election.setPlayerVote(player.getUuid(), candidateId);
            election.addVote(candidateId);

            // Save the election
            ElectionManager.getInstance().saveElection(town, election);

            // Get candidate name
            String candidateName = "Unknown";
            TownPlayer candidatePlayer = town.getPlayer(candidateId);

            if (candidatePlayer != null) {
                candidateName = candidatePlayer.getName();
            } else {
                // Try to get the name from the server
                ServerPlayerEntity candidateEntity = server.getPlayerManager().getPlayer(candidateId);

                if (candidateEntity != null) {
                    candidateName = candidateEntity.getName().getString();
                }
            }

            // Create response packet
            PacketByteBuf responseBuf = NetworkManager.createPacket();

            // Write town UUID
            responseBuf.writeUuid(townId);

            // Write success
            responseBuf.writeBoolean(true);

            // Write candidate UUID
            responseBuf.writeUuid(candidateId);

            // Write candidate name
            responseBuf.writeString(candidateName);

            // Send response to player
            NetworkManager.sendToPlayer(player, NetworkConstants.ELECTION_VOTE_RESPONSE, responseBuf);

            // Notify the player
            player.sendMessage(Text.literal("Vote cast for " + candidateName).formatted(Formatting.GREEN), false);

            // Notify all online players in the town
            for (UUID playerId : town.getPlayers()) {
                if (!playerId.equals(player.getUuid())) {
                    ServerPlayerEntity townPlayer = server.getPlayerManager().getPlayer(playerId);

                    if (townPlayer != null) {
                        townPlayer.sendMessage(Text.literal(player.getName().getString() + " voted in the election").formatted(Formatting.GOLD), false);
                    }
                }
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling election vote request: " + e.getMessage());
        }
    }
}

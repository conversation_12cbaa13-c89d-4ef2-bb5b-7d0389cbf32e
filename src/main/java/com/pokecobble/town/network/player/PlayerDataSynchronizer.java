package com.pokecobble.town.network.player;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.PlayerDataManager;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.TownPlayer;
import com.pokecobble.town.TownPlayerRank;
import com.pokecobble.town.network.NetworkConstants;
import com.pokecobble.town.network.NetworkManager;
import com.pokecobble.town.network.PacketValidator;
import com.pokecobble.town.network.security.PacketAuthenticator;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking;
import net.minecraft.network.PacketByteBuf;
import net.fabricmc.fabric.api.networking.v1.PacketSender;
import net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.network.ClientPlayNetworkHandler;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayerEntity;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Handles synchronization of player data between server and client.
 */
public class PlayerDataSynchronizer {

    /**
     * Registers client-side packet handlers.
     */
    @Environment(EnvType.CLIENT)
    public static void registerClientHandlers() {
        // Register player data sync handler
        ClientPlayNetworking.registerGlobalReceiver(
                NetworkConstants.PLAYER_DATA_SYNC,
                PlayerDataSynchronizer::handlePlayerDataSync
        );
    }

    /**
     * Handles player data sync packets on the client side.
     */
    @Environment(EnvType.CLIENT)
    private static void handlePlayerDataSync(net.minecraft.client.MinecraftClient client,
                                           net.minecraft.client.network.ClientPlayNetworkHandler handler,
                                           PacketByteBuf buf,
                                           PacketSender sender) {
        try {
            // Read player UUID
            UUID playerId = buf.readUuid();

            // Read player name
            String playerName = buf.readString(NetworkConstants.MAX_STRING_LENGTH);

            // Read player rank
            int rankOrdinal = buf.readInt();
            TownPlayerRank rank = TownPlayerRank.values()[rankOrdinal];

            // Read permissions
            Map<String, Map<String, Boolean>> permissions = new HashMap<>();
            int categoryCount = buf.readInt();

            for (int i = 0; i < categoryCount; i++) {
                String category = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                int permissionCount = buf.readInt();

                Map<String, Boolean> categoryPermissions = new HashMap<>();
                for (int j = 0; j < permissionCount; j++) {
                    String permission = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                    boolean value = buf.readBoolean();
                    categoryPermissions.put(permission, value);
                }

                permissions.put(category, categoryPermissions);
            }

            // Read town ID
            UUID townId = buf.readUuid();

            // Read data version
            int dataVersion = buf.readInt();

            // Create or update player data
            TownPlayer townPlayer = new TownPlayer(playerId, playerName, rank);
            townPlayer.setDataVersion(dataVersion);

            // Set permissions
            for (Map.Entry<String, Map<String, Boolean>> entry : permissions.entrySet()) {
                townPlayer.setCategoryPermissions(entry.getKey(), entry.getValue());
            }

            // Update client cache
            com.pokecobble.town.client.ClientPlayerManager.getInstance().updatePlayer(playerId, townPlayer, dataVersion);
            com.pokecobble.town.client.ClientPlayerManager.getInstance().updatePlayerPermissions(playerId, permissions);

            // If this is the current player, update current player cache
            if (client.player != null && playerId.equals(client.player.getUuid())) {
                com.pokecobble.town.client.ClientPlayerManager.getInstance().setCurrentPlayer(playerId, townPlayer, dataVersion);

                // Update town association
                if (townId != null) {
                    com.pokecobble.town.client.ClientTownManager.getInstance().setPlayerTown(townId, dataVersion);
                }
            }

            // Fire sync event
            com.pokecobble.town.client.ClientSyncManager.getInstance().onPlayerUpdated(playerId, dataVersion);

            Pokecobbleclaim.LOGGER.debug("Synchronized player data for " + playerName + " (version " + dataVersion + ")");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling player data sync: " + e.getMessage());
        }
    }

    /**
     * Registers server-side packet handlers.
     */
    public static void registerServerHandlers() {
        // No server-side handlers needed for now
        // We only send data from server to client
    }
    // Map to track player data versions
    private static final Map<UUID, Integer> playerDataVersions = new HashMap<>();

    /**
     * Synchronizes player data to the client.
     *
     * @param server The server instance
     * @param playerId The UUID of the player to synchronize
     */
    public static void syncPlayerData(MinecraftServer server, UUID playerId) {
        // Validate inputs
        try {
            PacketValidator.validateUUID(playerId);
        } catch (IllegalArgumentException e) {
            Pokecobbleclaim.LOGGER.error("Invalid player ID for syncPlayerData: " + e.getMessage());
            return;
        }

        if (server == null) {
            Pokecobbleclaim.LOGGER.error("Server is null in syncPlayerData");
            return;
        }

        if (server.getPlayerManager() == null) {
            return; // Server not fully initialized yet
        }

        // Get player entity
        ServerPlayerEntity playerEntity = server.getPlayerManager().getPlayer(playerId);
        if (playerEntity == null) {
            return; // Player not online
        }

        // Get player data
        TownPlayer townPlayer = PlayerDataManager.getInstance().getPlayer(playerId);
        if (townPlayer == null) {
            return; // No player data
        }

        // Check if player data has changed
        int currentVersion = townPlayer.getDataVersion();
        int knownVersion = playerDataVersions.getOrDefault(playerId, -1);

        if (currentVersion == knownVersion) {
            return; // No changes
        }

        // Update known version
        playerDataVersions.put(playerId, currentVersion);

        // Get town ID
        UUID townId = TownManager.getInstance().getPlayerTownId(playerId);
        Town town = townId != null ? TownManager.getInstance().getTownById(townId) : null;

        try {
            // Create packet buffer
            PacketByteBuf buf = NetworkManager.createPacket();

            // Write player UUID
            buf.writeUuid(playerId);

            // Write player name
            buf.writeString(townPlayer.getName());

            // Write player rank
            buf.writeInt(townPlayer.getRank().ordinal());

            // Write player permissions
            Map<String, Map<String, Boolean>> permissions = townPlayer.getAllPermissions();
            buf.writeInt(permissions.size());

            for (Map.Entry<String, Map<String, Boolean>> entry : permissions.entrySet()) {
                buf.writeString(entry.getKey());

                Map<String, Boolean> categoryPermissions = entry.getValue();
                buf.writeInt(categoryPermissions.size());

                for (Map.Entry<String, Boolean> permEntry : categoryPermissions.entrySet()) {
                    buf.writeString(permEntry.getKey());
                    buf.writeBoolean(permEntry.getValue());
                }
            }

            // Write town UUID (or null)
            if (town != null) {
                buf.writeBoolean(true);
                buf.writeUuid(town.getId());
            } else {
                buf.writeBoolean(false);
            }

            // Write data version
            buf.writeInt(currentVersion);

            // Add authentication data
            try {
                if (!PacketAuthenticator.authenticatePacket(buf, playerId)) {
                    Pokecobbleclaim.LOGGER.warn("Failed to authenticate packet for player " + playerId);
                    return;
                }
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Authentication error for player " + playerId + ": " + e.getMessage());
                return;
            }

            // Send packet to player
            com.pokecobble.town.network.NetworkManager.sendToPlayer(playerEntity, NetworkConstants.PLAYER_DATA_SYNC, buf);

            Pokecobbleclaim.LOGGER.debug("Synchronized player data for " + playerId);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error synchronizing player data for " + playerId + ": " + e.getMessage());
        }
    }

    /**
     * Synchronizes player data to all players.
     *
     * @param server The server instance
     */
    public static void syncAllPlayerData(MinecraftServer server) {
        if (server == null || server.getPlayerManager() == null) {
            return;
        }

        for (ServerPlayerEntity player : server.getPlayerManager().getPlayerList()) {
            try {
                // Validate player
                PacketValidator.validatePlayer(player);

                // Sync player data
                syncPlayerData(server, player.getUuid());
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error synchronizing data for player " + player.getName().getString() + ": " + e.getMessage());
            }
        }
    }

    /**
     * Removes a player from version tracking.
     *
     * @param playerId The UUID of the player
     */
    public static void removePlayer(UUID playerId) {
        playerDataVersions.remove(playerId);
    }

    /**
     * Clears all version tracking data.
     */
    public static void clearVersionTracking() {
        playerDataVersions.clear();
    }
}

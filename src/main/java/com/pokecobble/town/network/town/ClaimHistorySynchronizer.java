package com.pokecobble.town.network.town;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.claim.ClaimHistoryEntry;
import com.pokecobble.town.claim.ClaimTag;
import com.pokecobble.town.network.NetworkConstants;
import com.pokecobble.town.network.NetworkManager;
import com.pokecobble.town.network.PacketValidator;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.network.ClientPlayNetworkHandler;
import net.minecraft.network.PacketByteBuf;
import net.fabricmc.fabric.api.networking.v1.PacketSender;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayNetworkHandler;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.util.Identifier;
import net.minecraft.util.math.ChunkPos;

import java.util.*;

/**
 * Handles synchronization of claim history data between server and client.
 */
public class ClaimHistorySynchronizer {
    // Packet identifiers
    private static final Identifier CLAIM_HISTORY_UPDATE = new Identifier(NetworkConstants.MOD_ID, "claim_history_update");
    private static final Identifier CLAIM_HISTORY_REQUEST = new Identifier(NetworkConstants.MOD_ID, "claim_history_request");

    // Version tracking for each player and town
    private static final Map<UUID, Map<UUID, Integer>> playerTownHistoryVersions = new HashMap<>();

    /**
     * Registers all packet handlers.
     */
    public static void registerHandlers() {
        // This method is kept for compatibility but is no longer used
        // Use registerClientHandlers and registerServerHandlers instead
    }

    /**
     * Registers client-side packet handlers.
     */
    @Environment(EnvType.CLIENT)
    public static void registerClientHandlers() {
        net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.registerGlobalReceiver(
            CLAIM_HISTORY_UPDATE, ClaimHistorySynchronizer::handleClaimHistoryUpdate);
    }

    /**
     * Registers server-side packet handlers.
     */
    public static void registerServerHandlers() {
        net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking.registerGlobalReceiver(
            CLAIM_HISTORY_REQUEST, ClaimHistorySynchronizer::handleClaimHistoryRequest);
    }



    /**
     * Synchronizes claim history for a town to all relevant players.
     * This should be called when a town's claim history changes.
     *
     * @param server The server instance
     * @param town The town whose claim history has changed
     */
    public static void syncClaimHistory(MinecraftServer server, Town town) {
        // Get all online players in the town
        List<ServerPlayerEntity> townPlayers = new ArrayList<>();
        for (UUID playerId : town.getPlayers()) {
            ServerPlayerEntity player = server.getPlayerManager().getPlayer(playerId);
            if (player != null) {
                townPlayers.add(player);
            }
        }

        // Send claim history update to all town players
        for (ServerPlayerEntity player : townPlayers) {
            sendClaimHistoryUpdate(player, town);
        }
    }

    /**
     * Sends claim history data to a player.
     *
     * @param player The player to send data to
     * @param town The town whose claim history to send
     */
    private static void sendClaimHistoryUpdate(ServerPlayerEntity player, Town town) {
        try {
            // Create packet buffer
            PacketByteBuf buf = NetworkManager.createPacket();

            // Write town UUID
            buf.writeUuid(town.getId());

            // Write town data version
            buf.writeInt(town.getDataVersion());

            // Get claim history
            List<ClaimHistoryEntry> history = town.getClaimHistory();

            // Write history entry count (limit to 50 most recent entries)
            int entryCount = Math.min(history.size(), 50);
            buf.writeInt(entryCount);

            // Write history entries (most recent first)
            for (int i = 0; i < entryCount; i++) {
                ClaimHistoryEntry entry = history.get(i);

                // Write action type
                buf.writeInt(entry.getAction().ordinal());

                // Write chunk position
                buf.writeInt(entry.getChunkPos().x);
                buf.writeInt(entry.getChunkPos().z);

                // Write timestamp
                buf.writeLong(entry.getTimestamp().getTime());

                // Write player ID
                buf.writeUuid(entry.getPlayerId());

                // Write player name
                buf.writeString(entry.getPlayerName());

                // Write tag data
                ClaimTag tag = entry.getTag();
                if (tag != null) {
                    // Tag exists
                    buf.writeBoolean(true);
                    buf.writeString(tag.getId().toString());
                    buf.writeString(tag.getName());
                    buf.writeInt(tag.getColor());
                } else {
                    // No tag
                    buf.writeBoolean(false);
                }

                // Write previous tag data (for MODIFY actions)
                ClaimTag prevTag = entry.getPreviousTag();
                if (prevTag != null) {
                    // Previous tag exists
                    buf.writeBoolean(true);
                    buf.writeString(prevTag.getId().toString());
                    buf.writeString(prevTag.getName());
                    buf.writeInt(prevTag.getColor());
                } else {
                    // No previous tag
                    buf.writeBoolean(false);
                }
            }

            // Send packet to player (bypass rate limiting for automatic claim history sync)
            NetworkManager.sendToPlayer(player, CLAIM_HISTORY_UPDATE, buf);

            // Update player's known version for this town's history
            updatePlayerTownHistoryVersion(player.getUuid(), town.getId(), town.getDataVersion());

            Pokecobbleclaim.LOGGER.debug("Sent claim history update for " + town.getName() + " to " + player.getName().getString());
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending claim history update: " + e.getMessage());
        }
    }

    /**
     * Handles claim history request packets on the server side.
     */
    private static void handleClaimHistoryRequest(MinecraftServer server, ServerPlayerEntity player,
                                                ServerPlayNetworkHandler handler, PacketByteBuf buf,
                                                PacketSender sender) {
        try {
            // Validate packet size
            PacketValidator.validatePacketSize(buf);

            // Read player UUID for validation
            UUID requestingPlayerId = buf.readUuid();

            // Validate that the requesting player is the same as the sender
            if (!player.getUuid().equals(requestingPlayerId)) {
                Pokecobbleclaim.LOGGER.warn("Player " + player.getName().getString() + " tried to request claim history for another player");
                return;
            }

            // Read town UUID
            UUID townId = buf.readUuid();

            // Get town
            Town town = TownManager.getInstance().getTownById(townId);
            if (town == null) {
                Pokecobbleclaim.LOGGER.warn("Player " + player.getName().getString() + " requested claim history for unknown town: " + townId);
                return;
            }

            // Check if player is in the town
            if (!town.getPlayers().contains(player.getUuid())) {
                Pokecobbleclaim.LOGGER.warn("Player " + player.getName().getString() + " requested claim history for town they are not in: " + town.getName());
                return;
            }

            // Send claim history update
            sendClaimHistoryUpdate(player, town);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling claim history request: " + e.getMessage());
        }
    }

    /**
     * Handles claim history update packets on the client side.
     */
    @Environment(EnvType.CLIENT)
    private static void handleClaimHistoryUpdate(MinecraftClient client, ClientPlayNetworkHandler handler,
                                               PacketByteBuf buf, PacketSender sender) {
        try {
            // Read town UUID
            UUID townId = buf.readUuid();

            // Read town data version
            int dataVersion = buf.readInt();

            // Get town
            Town town = TownManager.getInstance().getTownById(townId);
            if (town == null) {
                Pokecobbleclaim.LOGGER.warn("Received claim history update for unknown town: " + townId);
                return;
            }

            // Read history entry count
            int entryCount = buf.readInt();

            // Create a new list to hold the history entries
            List<ClaimHistoryEntry> history = new ArrayList<>(entryCount);

            // Read history entries
            for (int i = 0; i < entryCount; i++) {
                // Read action type
                int actionOrdinal = buf.readInt();
                ClaimHistoryEntry.ActionType action = ClaimHistoryEntry.ActionType.values()[actionOrdinal];

                // Read chunk position
                int chunkX = buf.readInt();
                int chunkZ = buf.readInt();
                ChunkPos chunkPos = new ChunkPos(chunkX, chunkZ);

                // Read timestamp
                long timestamp = buf.readLong();
                Date date = new Date(timestamp);

                // Read player ID
                UUID playerId = buf.readUuid();

                // Read player name
                String playerName = buf.readString(NetworkConstants.MAX_STRING_LENGTH);

                // Read tag data
                ClaimTag tag = null;
                if (buf.readBoolean()) {
                    // Tag exists
                    String tagId = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                    String tagName = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                    int tagColor = buf.readInt();
                    tag = createTagWithId(tagId, tagName, tagColor);
                }

                // Read previous tag data
                ClaimTag prevTag = null;
                if (buf.readBoolean()) {
                    // Previous tag exists
                    String prevTagId = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                    String prevTagName = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                    int prevTagColor = buf.readInt();
                    prevTag = createTagWithId(prevTagId, prevTagName, prevTagColor);
                }

                // Create history entry
                ClaimHistoryEntry entry;
                if (prevTag != null) {
                    entry = new ClaimHistoryEntry(action, chunkPos, date, playerId, playerName, tag, prevTag);
                } else {
                    entry = new ClaimHistoryEntry(action, chunkPos, date, playerId, playerName, tag);
                }

                // Add entry to history
                history.add(entry);
            }

            // Update town's claim history
            // We need to clear the existing history and add all entries
            // This ensures the order is preserved
            town.clearClaimHistory();
            for (int i = history.size() - 1; i >= 0; i--) {
                town.addClaimHistoryEntry(history.get(i));
            }

            // Set data version
            town.setDataVersion(dataVersion);

            // Clear changed aspects since we just updated
            town.clearChangedAspects();

            Pokecobbleclaim.LOGGER.debug("Received claim history update for " + town.getName() + " with " + entryCount + " entries");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling claim history update: " + e.getMessage());
        }
    }

    /**
     * Requests claim history for a town from the server.
     * This is called when a player opens the claim history screen.
     *
     * @param townId The town ID
     */
    @Environment(EnvType.CLIENT)
    public static void requestClaimHistory(UUID townId) {
        try {
            // Create packet buffer
            PacketByteBuf buf = NetworkManager.createPacket();

            // Add player UUID for validation
            if (MinecraftClient.getInstance().player != null) {
                buf.writeUuid(MinecraftClient.getInstance().player.getUuid());
            } else {
                return; // Can't send without a player
            }

            // Write town UUID
            buf.writeUuid(townId);

            // Send packet to server
            NetworkManager.sendToServer(CLAIM_HISTORY_REQUEST, buf);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error requesting claim history: " + e.getMessage());
        }
    }

    /**
     * Updates a player's known version for a town's history.
     *
     * @param playerId The player's UUID
     * @param townId The town's UUID
     * @param version The new version
     */
    private static void updatePlayerTownHistoryVersion(UUID playerId, UUID townId, int version) {
        playerTownHistoryVersions.computeIfAbsent(playerId, k -> new HashMap<>()).put(townId, version);
    }

    /**
     * Checks if a player needs an update for a town's history.
     *
     * @param playerId The player's UUID
     * @param town The town
     * @return True if the player needs an update, false otherwise
     */
    private static boolean shouldSendHistoryUpdate(UUID playerId, Town town) {
        // Get the player's known version for this town
        Map<UUID, Integer> playerVersions = playerTownHistoryVersions.get(playerId);
        if (playerVersions == null) {
            return true; // Player has no version info, send update
        }

        // Get the player's known version for this town
        Integer knownVersion = playerVersions.get(town.getId());
        if (knownVersion == null) {
            return true; // Player has no version for this town, send update
        }

        // Check if the town's version is newer than the player's known version
        return town.getDataVersion() > knownVersion;
    }

    /**
     * Resets all player town history versions.
     * This should be called when the server starts.
     */
    public static void resetAllPlayerTownHistoryVersions() {
        playerTownHistoryVersions.clear();
    }

    /**
     * Removes a player from the version tracking.
     * This should be called when a player disconnects.
     *
     * @param playerId The player's UUID
     */
    public static void removePlayer(UUID playerId) {
        playerTownHistoryVersions.remove(playerId);
    }

    /**
     * Creates a ClaimTag with a specific ID.
     * This is a helper method for deserialization.
     *
     * @param tagId The tag ID as a string
     * @param tagName The tag name
     * @param tagColor The tag color
     * @return A new ClaimTag with the specified ID
     */
    private static ClaimTag createTagWithId(String tagId, String tagName, int tagColor) {
        // Create a new tag with the given name and color
        ClaimTag tag = new ClaimTag(tagName, tagColor);

        // We can't set the ID directly since it's final, but for network purposes
        // we can create a tag with the same properties and consider it equivalent
        return tag;
    }
}

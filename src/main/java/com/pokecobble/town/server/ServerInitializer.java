package com.pokecobble.town.server;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.PlayerDataManager;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.config.BackupConfig;
import com.pokecobble.town.data.PlayerDataUtils;
import com.pokecobble.town.logging.ErrorLogger;
import com.pokecobble.town.network.chunk.ChunkDataSynchronizer;
import com.pokecobble.town.network.player.PlayerDataSynchronizer;
import com.pokecobble.town.network.town.TownDataSynchronizer;
import net.fabricmc.api.DedicatedServerModInitializer;
import net.fabricmc.fabric.api.event.lifecycle.v1.ServerLifecycleEvents;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayerEntity;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.UUID;

/**
 * Initializes server-side components of the mod.
 * This class is responsible for setting up the server instance in the TownManager
 * and registering server lifecycle events.
 */
public class ServerInitializer implements DedicatedServerModInitializer {
    // Track the last backup time
    private static Instant lastBackupTime = Instant.now();

    // Flag to indicate if the server is shutting down
    private static volatile boolean isShuttingDown = false;
    @Override
    public void onInitializeServer() {
        Pokecobbleclaim.LOGGER.info("Initializing PokeCobbleClaim server components");

        // Load backup configuration
        BackupConfig.load();

        // Register server start event
        ServerLifecycleEvents.SERVER_STARTING.register(this::onServerStarting);

        // Register server stop event
        ServerLifecycleEvents.SERVER_STOPPING.register(this::onServerStopping);

        // Register player join event
        net.fabricmc.fabric.api.networking.v1.ServerPlayConnectionEvents.JOIN.register((handler, sender, server) -> {
            ServerPlayerEntity player = handler.getPlayer();
            UUID playerId = player.getUuid();

            // Handle player join in PlayerDataManager
            PlayerDataManager.getInstance().onPlayerJoin(player);

            // When a player joins, synchronize town and chunk data to them
            TownManager.getInstance().synchronizeTownData();
            ChunkDataSynchronizer.syncChunkData(server);

            // Send the current town list to the new player
            // This ensures they see all existing towns immediately, including loaded towns
            TownDataSynchronizer.sendTownListToNewPlayer(player);

            // Ensure all town data is synchronized to the new player
            // This is critical for showing loaded towns after server restart
            server.execute(() -> {
                // Sync all town data to the new player
                TownDataSynchronizer.syncPlayerTownData(server, playerId);

                // Ensure player's town membership is synchronized
                UUID townId = TownManager.getInstance().getPlayerTownId(playerId);
                if (townId != null) {
                    // Player is in a town, ensure this is synchronized to client
                    Town playerTown = TownManager.getInstance().getTownById(townId);
                    if (playerTown != null) {
                        // Force synchronization of the player's town data, especially player ranks
                        TownDataSynchronizer.syncTownData(server, playerTown);

                        // Synchronize town settings to the player with a small delay to ensure all data is loaded
                        server.execute(() -> {
                            try {
                                // Small delay to ensure all town data is fully loaded
                                Thread.sleep(100);
                                com.pokecobble.config.ConfigSynchronizer.syncTownSettingsToPlayer(server, playerId);
                                Pokecobbleclaim.LOGGER.info("Synchronized town settings for player " + player.getName().getString() + " to town " + townId);
                            } catch (Exception e) {
                                Pokecobbleclaim.LOGGER.warn("Failed to sync town settings to player " + player.getName().getString() + ": " + e.getMessage());
                            }
                        });

                        Pokecobbleclaim.LOGGER.info("Synchronized town membership and player ranks for player " + player.getName().getString() + " to town " + townId);
                    }
                } else {
                    Pokecobbleclaim.LOGGER.info("Player " + player.getName().getString() + " is not in any town");

                    // Even if player is not in a town, sync all town settings so they can see all towns properly
                    server.execute(() -> {
                        try {
                            Thread.sleep(100);
                            com.pokecobble.config.ConfigSynchronizer.syncTownSettingsToPlayer(server, playerId);
                            Pokecobbleclaim.LOGGER.info("Synchronized all town settings for player " + player.getName().getString() + " (not in any town)");
                        } catch (Exception e) {
                            Pokecobbleclaim.LOGGER.warn("Failed to sync all town settings to player " + player.getName().getString() + ": " + e.getMessage());
                        }
                    });
                }

                // Log the number of towns available for this player
                int townCount = TownManager.getInstance().getAllTowns().size();
                Pokecobbleclaim.LOGGER.info("Synchronized " + townCount + " towns to player " + player.getName().getString());
            });
        });

        // Register player leave event
        net.fabricmc.fabric.api.networking.v1.ServerPlayConnectionEvents.DISCONNECT.register((handler, server) -> {
            // Handle player leave in PlayerDataManager
            PlayerDataManager.getInstance().onPlayerLeave(handler.getPlayer());

            // When a player leaves, remove them from version tracking
            com.pokecobble.town.network.town.TownDataSynchronizer.removePlayer(handler.getPlayer().getUuid());
            ChunkDataSynchronizer.removePlayer(handler.getPlayer().getUuid());
            PlayerDataSynchronizer.removePlayer(handler.getPlayer().getUuid());

            // Clear status subscriptions
            com.pokecobble.status.RealTimeStatusManager.getInstance().clearPlayerSubscriptions(handler.getPlayer().getUuid());

            // Clear config subscriptions
            com.pokecobble.config.ConfigSynchronizer.clearPlayerConfig(handler.getPlayer().getUuid());
        });

        Pokecobbleclaim.LOGGER.info("PokeCobbleClaim server components initialized successfully");
    }

    /**
     * Called when the server is starting.
     * Sets the server instance in the TownManager.
     *
     * @param server The server instance
     */
    private void onServerStarting(MinecraftServer server) {
        Pokecobbleclaim.LOGGER.info("Setting server instance in TownManager and PlayerDataManager");

        // Set server instance in TownManager
        TownManager.getInstance().setServer(server);

        // Set server instance in PlayerDataManager
        PlayerDataManager.getInstance().setServer(server);

        // Set server instance in ErrorLogger
        ErrorLogger.getInstance().setServer(server);
        ErrorLogger.getInstance().logInfo("Server started", "Server");

        // Initialize real-time status manager
        com.pokecobble.status.RealTimeStatusManager.getInstance().initialize(server);

        // Load town data from disk to ensure it's available when players join
        // This is critical for restoring player-town relationships after server restart
        Pokecobbleclaim.LOGGER.info("Loading town data during server startup");
        com.pokecobble.town.data.TownDataStorage.loadTowns();

        // Perform health check on town settings files before loading
        // This repairs any corrupted or invalid settings files
        Pokecobbleclaim.LOGGER.info("Performing town settings health check during server startup");
        com.pokecobble.town.config.TownSettingsManager.performSettingsHealthCheck();

        // Load all town settings from disk to ensure they're available in memory
        // This is critical for settings persistence across server restarts
        Pokecobbleclaim.LOGGER.info("Loading town settings during server startup");
        com.pokecobble.town.config.TownSettingsManager.loadAllTownSettingsFromDisk();

        // Apply persisted settings to all loaded towns
        // This ensures town objects reflect the persisted settings after server restart
        Pokecobbleclaim.LOGGER.info("Applying persisted settings to all loaded towns");
        com.pokecobble.town.config.TownSettingsManager.applyPersistedSettingsToAllTowns();

        // After loading towns, ensure all town data is ready for synchronization
        // Broadcast town list to prepare for when players join
        TownManager townManager = TownManager.getInstance();
        if (!townManager.getAllTowns().isEmpty()) {
            Pokecobbleclaim.LOGGER.info("Preparing town data synchronization for " + townManager.getAllTowns().size() + " loaded towns");

            // Verify that town settings were properly loaded for all towns
            int townsWithSettings = 0;
            int townsWithCustomSettings = 0;
            int townsWithLoadedSettings = 0;
            for (com.pokecobble.town.Town town : townManager.getAllTowns()) {
                // Check if town has any existing settings (not defaults)
                java.util.Map<String, Object> existingSettings = com.pokecobble.town.config.TownSettingsManager.getExistingTownSettings(town.getId());
                if (existingSettings != null && !existingSettings.isEmpty()) {
                    townsWithCustomSettings++;
                    townsWithLoadedSettings++;
                }

                // This will create defaults if none exist, but we want to track actual loaded settings
                java.util.Map<String, Object> settings = com.pokecobble.town.config.TownSettingsManager.getTownSettings(town.getId());
                if (settings != null && !settings.isEmpty()) {
                    townsWithSettings++;
                }
            }
            Pokecobbleclaim.LOGGER.info("Town settings status: " + townsWithSettings + " total, " + townsWithLoadedSettings + " loaded from disk, " + townsWithCustomSettings + " with custom settings out of " + townManager.getAllTowns().size() + " towns");

            // Sync all loaded town settings to all online players
            // This ensures players who are already online get the loaded settings
            try {
                com.pokecobble.config.ConfigSynchronizer.syncAllTownSettingsToAllPlayers(server);
                Pokecobbleclaim.LOGGER.info("Synchronized all loaded town settings to online players");
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.warn("Failed to sync town settings to online players during startup: " + e.getMessage());
            }
        } else {
            Pokecobbleclaim.LOGGER.info("No towns were loaded from disk - starting with empty town list");
        }

        // Reset all player data versions
        TownManager.getInstance().resetAllPlayerDataVersions();
        PlayerDataSynchronizer.clearVersionTracking();

        // Register periodic town data synchronization
        registerPeriodicSync(server);
    }

    /**
     * Called when the server is stopping.
     * Clears the server instance in the TownManager.
     *
     * @param server The server instance
     */
    private void onServerStopping(MinecraftServer server) {
        Pokecobbleclaim.LOGGER.info("Saving all data and clearing server instances");

        // Set shutdown flag to stop periodic operations
        isShuttingDown = true;

        try {
            // Save all player data
            PlayerDataManager.getInstance().saveAllPlayers();
            Pokecobbleclaim.LOGGER.info("Player data saved successfully");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error saving player data during shutdown: " + e.getMessage());
        }

        try {
            // Save all town and player-town relationship data
            TownManager.getInstance().saveAllData();
            Pokecobbleclaim.LOGGER.info("Town data saved successfully");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error saving town data during shutdown: " + e.getMessage());
        }

        // Create backup if enabled
        try {
            if (BackupConfig.isBackupOnServerStopEnabled()) {
                Pokecobbleclaim.LOGGER.info("Creating player data backup on server stop");
                String backupPath = PlayerDataUtils.backupAllPlayerData();
                if (backupPath != null) {
                    Pokecobbleclaim.LOGGER.info("Backup created at: " + backupPath);

                    // Clean up old backups
                    int deleted = PlayerDataUtils.cleanupOldBackups(BackupConfig.getMaxBackups());
                    if (deleted > 0) {
                        Pokecobbleclaim.LOGGER.info("Cleaned up " + deleted + " old backups");
                    }
                } else {
                    Pokecobbleclaim.LOGGER.warn("Failed to create backup on server stop");
                }
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error during backup process on server stop: " + e.getMessage());
        }

        // Shutdown real-time status manager
        try {
            com.pokecobble.status.RealTimeStatusManager.getInstance().shutdown();
            Pokecobbleclaim.LOGGER.info("Real-time status manager shut down successfully");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.warn("Error shutting down real-time status manager: " + e.getMessage());
        }

        // Shutdown synchronization manager
        try {
            com.pokecobble.town.network.SynchronizationManager.getInstance().shutdown();
            Pokecobbleclaim.LOGGER.info("Synchronization manager shut down successfully");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.warn("Error shutting down synchronization manager: " + e.getMessage());
        }

        // Shutdown backup executor
        try {
            com.pokecobble.town.data.PlayerDataUtils.shutdownBackupExecutor();
            Pokecobbleclaim.LOGGER.info("Backup executor shut down successfully");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.warn("Error shutting down backup executor: " + e.getMessage());
        }

        // Give a moment for all shutdown operations to complete
        try {
            Thread.sleep(500);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // Clear server instance in TownManager
        try {
            TownManager.getInstance().setServer(null);
            Pokecobbleclaim.LOGGER.info("TownManager server instance cleared");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error clearing TownManager server instance: " + e.getMessage());
        }

        // Clear server instance in PlayerDataManager
        try {
            PlayerDataManager.getInstance().setServer(null);
            Pokecobbleclaim.LOGGER.info("PlayerDataManager server instance cleared");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error clearing PlayerDataManager server instance: " + e.getMessage());
        }

        Pokecobbleclaim.LOGGER.info("PokeCobbleClaim server shutdown completed");
    }

    /**
     * Registers periodic town data synchronization.
     * This ensures that town data is synchronized even if no explicit changes are made.
     *
     * @param server The server instance
     */
    private void registerPeriodicSync(MinecraftServer server) {
        // Register a tick callback to periodically synchronize town data
        net.fabricmc.fabric.api.event.lifecycle.v1.ServerTickEvents.END_SERVER_TICK.register(s -> {
            // Skip all operations if server is shutting down
            if (isShuttingDown) {
                return;
            }

            // Synchronize town, chunk, and player data every 5 minutes (6000 ticks)
            if (s.getTicks() % 6000 == 0) {
                TownManager.getInstance().synchronizeTownData();
                ChunkDataSynchronizer.syncChunkData(s);
                PlayerDataSynchronizer.syncAllPlayerData(s);

                // Save all player data to disk
                PlayerDataManager.getInstance().saveAllPlayers();

                // Check if it's time for a backup
                if (BackupConfig.isAutoBackupEnabled()) {
                    Instant now = Instant.now();
                    long hoursSinceLastBackup = ChronoUnit.HOURS.between(lastBackupTime, now);

                    if (hoursSinceLastBackup >= BackupConfig.getBackupIntervalHours()) {
                        Pokecobbleclaim.LOGGER.info("Creating scheduled player data backup");
                        String backupPath = PlayerDataUtils.backupAllPlayerData();

                        if (backupPath != null) {
                            Pokecobbleclaim.LOGGER.info("Backup created at: " + backupPath);
                            lastBackupTime = now;

                            // Clean up old backups
                            int deleted = PlayerDataUtils.cleanupOldBackups(BackupConfig.getMaxBackups());
                            if (deleted > 0) {
                                Pokecobbleclaim.LOGGER.info("Cleaned up " + deleted + " old backups");
                            }
                        } else {
                            Pokecobbleclaim.LOGGER.warn("Failed to create scheduled backup");
                        }
                    }
                }
            }

            // Update towns (elections, etc.) every second (20 ticks)
            if (s.getTicks() % 20 == 0 && !isShuttingDown) {
                TownManager.getInstance().update();
            }

            // Check watching players' balances every 5 seconds (100 ticks)
            // The internal check in MoneyNetworkHandler will limit the actual check frequency
            // and handle errors gracefully to prevent lag and rate limiting
            if (s.getTicks() % 100 == 0) {
                com.pokecobble.town.network.money.MoneyNetworkHandler.checkWatchingPlayersBalances(s);
            }
        });
    }
}

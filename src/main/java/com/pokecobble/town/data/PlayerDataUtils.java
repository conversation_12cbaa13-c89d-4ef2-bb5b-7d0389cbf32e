package com.pokecobble.town.data;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.config.BackupConfig;
import net.fabricmc.loader.api.FabricLoader;

import java.io.*;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.stream.Collectors;
import java.util.zip.*;
import java.util.zip.Deflater;

/**
 * Utility class for player data operations like backup and compression.
 */
public class PlayerDataUtils {
    // Thread pool for backup operations
    private static final ExecutorService BACKUP_EXECUTOR = Executors.newSingleThreadExecutor(new BackupThreadFactory());

    // Map to track file hashes for incremental backups
    private static final Map<String, String> fileHashes = new ConcurrentHashMap<>();

    // Map to track last backup time for each player
    private static final Map<String, Long> lastPlayerBackupTime = new ConcurrentHashMap<>();

    // Last full backup time
    private static long lastFullBackupTime = System.currentTimeMillis();

    // Last differential backup base time
    private static long lastDifferentialBaseTime = 0;

    // Flag to indicate if a backup is in progress
    private static volatile boolean backupInProgress = false;

    // Custom thread factory for backup operations
    private static class BackupThreadFactory implements ThreadFactory {
        @Override
        public Thread newThread(Runnable r) {
            Thread thread = new Thread(r, "PlayerDataBackup");
            thread.setPriority(BackupConfig.getBackupThreadPriority());
            thread.setDaemon(true); // Make it a daemon thread so it doesn't prevent JVM shutdown
            return thread;
        }
    }
    // Constants for file paths
    private static final String DATA_FOLDER = "pokecobbleclaim";
    private static final String PLAYERS_FOLDER = "players";
    private static final String BACKUPS_FOLDER = "backups";

    // Date format for backup files
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd_HH-mm-ss");

    // Backup types
    private static final String BACKUP_TYPE_FULL = "full";
    private static final String BACKUP_TYPE_INCREMENTAL = "incremental";
    private static final String BACKUP_TYPE_DIFFERENTIAL = "differential";

    /**
     * Calculates the MD5 hash of a file.
     *
     * @param file The file to hash
     * @return The MD5 hash as a hex string, or null if hashing failed
     */
    private static String calculateFileHash(File file) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            try (InputStream is = new BufferedInputStream(new FileInputStream(file), BackupConfig.getBufferSize())) {
                byte[] buffer = new byte[BackupConfig.getBufferSize()];
                int read;
                while ((read = is.read(buffer)) != -1) {
                    md.update(buffer, 0, read);
                }
            }

            byte[] digest = md.digest();
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to calculate file hash for " + file.getPath() + ": " + e.getMessage());
            return null;
        }
    }

    /**
     * Determines if a file has changed since the last backup.
     *
     * @param file The file to check
     * @return true if the file has changed, false otherwise
     */
    private static boolean hasFileChanged(File file) {
        String filePath = file.getAbsolutePath();
        String currentHash = calculateFileHash(file);

        if (currentHash == null) {
            // If we can't calculate the hash, assume the file has changed
            return true;
        }

        String previousHash = fileHashes.get(filePath);
        if (previousHash == null || !previousHash.equals(currentHash)) {
            // File is new or has changed
            fileHashes.put(filePath, currentHash);
            return true;
        }

        // File hasn't changed
        return false;
    }

    /**
     * Determines the type of backup to perform based on configuration and timing.
     *
     * @return The backup type (full, incremental, or differential)
     */
    private static String determineBackupType() {
        // If incremental backups are disabled, always do full backups
        if (!BackupConfig.isIncrementalBackupsEnabled()) {
            return BACKUP_TYPE_FULL;
        }

        // If differential backups are enabled and we have a base, do a differential backup
        if (BackupConfig.isDifferentialBackupsEnabled() && lastDifferentialBaseTime > 0) {
            // Do a full backup every 7 days to establish a new base
            long daysSinceLastFullBackup = (System.currentTimeMillis() - lastFullBackupTime) / (1000 * 60 * 60 * 24);
            if (daysSinceLastFullBackup >= 7) {
                return BACKUP_TYPE_FULL;
            }
            return BACKUP_TYPE_DIFFERENTIAL;
        }

        // Otherwise, do an incremental backup
        return BACKUP_TYPE_INCREMENTAL;
    }

    /**
     * Creates a backup of all player data.
     *
     * @return The path to the created backup file, or null if backup failed
     */
    public static String backupAllPlayerData() {
        // Check if a backup is already in progress
        if (backupInProgress) {
            Pokecobbleclaim.LOGGER.warn("A backup is already in progress, skipping this request");
            return null;
        }

        // Set backup in progress flag
        backupInProgress = true;

        try {
            // Get the players directory
            File playersDir = getPlayersDirectory();
            if (!playersDir.exists() || !playersDir.isDirectory()) {
                Pokecobbleclaim.LOGGER.warn("No player data to backup");
                backupInProgress = false;
                return null;
            }

            // Create backups directory if it doesn't exist
            File backupsDir = getBackupsDirectory();
            if (!backupsDir.exists()) {
                backupsDir.mkdirs();
            }

            // Determine backup type
            String backupType = determineBackupType();

            // Create backup file name with timestamp and type
            String timestamp = DATE_FORMAT.format(new Date());
            String backupFileName = "player_data_" + timestamp + "_" + backupType + ".zip";
            File backupFile = new File(backupsDir, backupFileName);

            // Create metadata file to store backup information
            File metadataFile = new File(backupsDir, "backup_metadata.properties");
            Properties metadata = new Properties();

            // Load existing metadata if available
            if (metadataFile.exists()) {
                try (FileReader reader = new FileReader(metadataFile)) {
                    metadata.load(reader);
                } catch (IOException e) {
                    Pokecobbleclaim.LOGGER.warn("Failed to load backup metadata: " + e.getMessage());
                }
            }

            // Update metadata with this backup
            metadata.setProperty("last_backup_time", String.valueOf(System.currentTimeMillis()));
            metadata.setProperty("last_backup_type", backupType);
            metadata.setProperty("last_backup_file", backupFile.getName());

            // For incremental backups, store the base backup
            if (backupType.equals(BACKUP_TYPE_INCREMENTAL)) {
                String baseBackup = metadata.getProperty("last_full_backup_file", "");
                metadata.setProperty("incremental_base", baseBackup);
            }

            // For differential backups, store the base backup
            if (backupType.equals(BACKUP_TYPE_DIFFERENTIAL)) {
                String baseBackup = metadata.getProperty("last_full_backup_file", "");
                metadata.setProperty("differential_base", baseBackup);
            }

            // If this is a full backup, update the base information
            if (backupType.equals(BACKUP_TYPE_FULL)) {
                metadata.setProperty("last_full_backup_time", String.valueOf(System.currentTimeMillis()));
                metadata.setProperty("last_full_backup_file", backupFile.getName());
                lastFullBackupTime = System.currentTimeMillis();
                lastDifferentialBaseTime = System.currentTimeMillis();

                // Clear file hashes for a fresh start
                fileHashes.clear();
            }

            // Save metadata
            try (FileWriter writer = new FileWriter(metadataFile)) {
                metadata.store(writer, "PokeCobbleClaim Backup Metadata");
            } catch (IOException e) {
                Pokecobbleclaim.LOGGER.warn("Failed to save backup metadata: " + e.getMessage());
            }

            // Create ZIP file with appropriate compression level
            try (ZipOutputStream zipOut = new ZipOutputStream(new BufferedOutputStream(
                    new FileOutputStream(backupFile), BackupConfig.getBufferSize()))) {

                // Set compression level
                zipOut.setLevel(BackupConfig.getCompressionLevel());

                // Add metadata entry to the ZIP file
                ZipEntry metadataEntry = new ZipEntry("backup_info.properties");
                zipOut.putNextEntry(metadataEntry);

                // Write backup type and timestamp to the metadata entry
                Properties backupInfo = new Properties();
                backupInfo.setProperty("backup_type", backupType);
                backupInfo.setProperty("backup_time", timestamp);
                backupInfo.setProperty("backup_timestamp", String.valueOf(System.currentTimeMillis()));

                // For incremental/differential backups, include base information
                if (!backupType.equals(BACKUP_TYPE_FULL)) {
                    backupInfo.setProperty("base_backup", metadata.getProperty(
                            backupType.equals(BACKUP_TYPE_INCREMENTAL) ? "incremental_base" : "differential_base", ""));
                }

                // Save backup info to ZIP entry
                backupInfo.store(new OutputStreamWriter(zipOut), "PokeCobbleClaim Backup Information");
                zipOut.closeEntry();

                // Create a list to track processed files for incremental/differential backups
                final List<Path> processedFiles = new ArrayList<>();

                // Walk through all files in the players directory
                Files.walkFileTree(playersDir.toPath(), new SimpleFileVisitor<Path>() {
                    @Override
                    public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                        // Get relative path for ZIP entry
                        Path relativePath = playersDir.toPath().relativize(file);
                        File actualFile = file.toFile();

                        // For incremental backups, only include changed files
                        if (backupType.equals(BACKUP_TYPE_INCREMENTAL) || backupType.equals(BACKUP_TYPE_DIFFERENTIAL)) {
                            // For differential backups, compare against the last full backup
                            // For incremental backups, compare against the last backup of any type
                            boolean shouldInclude;

                            if (backupType.equals(BACKUP_TYPE_DIFFERENTIAL)) {
                                // For differential, include if changed since last full backup
                                long lastModified = actualFile.lastModified();
                                shouldInclude = lastModified > lastDifferentialBaseTime || hasFileChanged(actualFile);
                            } else {
                                // For incremental, include if changed since last backup
                                shouldInclude = hasFileChanged(actualFile);
                            }

                            if (!shouldInclude) {
                                return FileVisitResult.CONTINUE;
                            }
                        }

                        // Add file to ZIP
                        ZipEntry zipEntry = new ZipEntry(relativePath.toString());
                        zipOut.putNextEntry(zipEntry);

                        // Write file content to ZIP with buffering
                        try (BufferedInputStream bis = new BufferedInputStream(
                                new FileInputStream(actualFile), BackupConfig.getBufferSize())) {
                            byte[] buffer = new byte[BackupConfig.getBufferSize()];
                            int len;
                            while ((len = bis.read(buffer)) > 0) {
                                zipOut.write(buffer, 0, len);
                            }
                        }

                        zipOut.closeEntry();
                        processedFiles.add(relativePath);

                        return FileVisitResult.CONTINUE;
                    }
                });

                // Add a manifest of included files
                ZipEntry manifestEntry = new ZipEntry("file_manifest.txt");
                zipOut.putNextEntry(manifestEntry);

                try (PrintWriter pw = new PrintWriter(new OutputStreamWriter(zipOut))) {
                    for (Path path : processedFiles) {
                        pw.println(path.toString());
                    }
                }

                zipOut.closeEntry();
            }

            // Verify the backup if enabled
            if (BackupConfig.isVerifyBackupsEnabled()) {
                if (!verifyBackup(backupFile)) {
                    Pokecobbleclaim.LOGGER.error("Backup verification failed for " + backupFile.getPath());
                    backupFile.delete(); // Delete the corrupted backup
                    backupInProgress = false;
                    return null;
                }
            }

            Pokecobbleclaim.LOGGER.info("Created " + backupType + " player data backup: " + backupFile.getPath());
            backupInProgress = false;
            return backupFile.getPath();
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to backup player data: " + e.getMessage());
            e.printStackTrace();
            backupInProgress = false;
            return null;
        }
    }

    /**
     * Verifies a backup file to ensure it's not corrupted.
     *
     * @param backupFile The backup file to verify
     * @return true if the backup is valid, false otherwise
     */
    private static boolean verifyBackup(File backupFile) {
        try {
            // Try to open and read the ZIP file
            try (ZipFile zipFile = new ZipFile(backupFile)) {
                // Check if the backup contains the metadata entry
                ZipEntry metadataEntry = zipFile.getEntry("backup_info.properties");
                if (metadataEntry == null) {
                    Pokecobbleclaim.LOGGER.warn("Backup verification failed: Missing metadata entry");
                    return false;
                }

                // Try to read the metadata
                try (InputStream is = zipFile.getInputStream(metadataEntry)) {
                    Properties backupInfo = new Properties();
                    backupInfo.load(is);

                    // Check if the backup type is valid
                    String backupType = backupInfo.getProperty("backup_type");
                    if (backupType == null || (!backupType.equals(BACKUP_TYPE_FULL) &&
                            !backupType.equals(BACKUP_TYPE_INCREMENTAL) &&
                            !backupType.equals(BACKUP_TYPE_DIFFERENTIAL))) {
                        Pokecobbleclaim.LOGGER.warn("Backup verification failed: Invalid backup type");
                        return false;
                    }
                }

                // Check if the manifest exists
                ZipEntry manifestEntry = zipFile.getEntry("file_manifest.txt");
                if (manifestEntry == null) {
                    Pokecobbleclaim.LOGGER.warn("Backup verification failed: Missing manifest entry");
                    return false;
                }

                // Read the manifest and verify each file
                List<String> manifestFiles = new ArrayList<>();
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(zipFile.getInputStream(manifestEntry)))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        manifestFiles.add(line);
                    }
                }

                // Check if all files in the manifest exist in the ZIP
                for (String filePath : manifestFiles) {
                    ZipEntry fileEntry = zipFile.getEntry(filePath);
                    if (fileEntry == null) {
                        Pokecobbleclaim.LOGGER.warn("Backup verification failed: Missing file " + filePath);
                        return false;
                    }

                    // Try to read the file to ensure it's not corrupted
                    try (InputStream is = zipFile.getInputStream(fileEntry)) {
                        byte[] buffer = new byte[BackupConfig.getBufferSize()];
                        while (is.read(buffer) > 0) {
                            // Just read the file to check for corruption
                        }
                    }
                }

                return true;
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Backup verification failed: " + e.getMessage());
            return false;
        }
    }

    /**
     * Creates a backup of a specific player's data.
     *
     * @param playerUuid The UUID of the player
     * @return The path to the created backup file, or null if backup failed
     */
    public static String backupPlayerData(String playerUuid) {
        // Update last backup time for this player
        lastPlayerBackupTime.put(playerUuid, System.currentTimeMillis());

        try {
            // Get the player directory
            File playerDir = new File(getPlayersDirectory(), playerUuid);
            if (!playerDir.exists() || !playerDir.isDirectory()) {
                Pokecobbleclaim.LOGGER.warn("No data to backup for player " + playerUuid);
                return null;
            }

            // Create backups directory if it doesn't exist
            File backupsDir = new File(getBackupsDirectory(), playerUuid);
            if (!backupsDir.exists()) {
                backupsDir.mkdirs();
            }

            // Create backup file name with timestamp
            String timestamp = DATE_FORMAT.format(new Date());
            String backupFileName = "player_data_" + timestamp + ".zip";
            File backupFile = new File(backupsDir, backupFileName);

            // Create ZIP file with appropriate compression level
            try (ZipOutputStream zipOut = new ZipOutputStream(new BufferedOutputStream(
                    new FileOutputStream(backupFile), BackupConfig.getBufferSize()))) {

                // Set compression level
                zipOut.setLevel(BackupConfig.getCompressionLevel());

                // Add metadata entry to the ZIP file
                ZipEntry metadataEntry = new ZipEntry("backup_info.properties");
                zipOut.putNextEntry(metadataEntry);

                // Write backup information to the metadata entry
                Properties backupInfo = new Properties();
                backupInfo.setProperty("player_uuid", playerUuid);
                backupInfo.setProperty("backup_time", timestamp);
                backupInfo.setProperty("backup_timestamp", String.valueOf(System.currentTimeMillis()));

                // Save backup info to ZIP entry
                backupInfo.store(new OutputStreamWriter(zipOut), "PokeCobbleClaim Player Backup Information");
                zipOut.closeEntry();

                // Create a list to track processed files
                final List<Path> processedFiles = new ArrayList<>();

                // Walk through all files in the player directory
                Files.walkFileTree(playerDir.toPath(), new SimpleFileVisitor<Path>() {
                    @Override
                    public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                        // Get relative path for ZIP entry
                        Path relativePath = playerDir.toPath().relativize(file);
                        ZipEntry zipEntry = new ZipEntry(relativePath.toString());
                        zipOut.putNextEntry(zipEntry);

                        // Write file content to ZIP with buffering
                        try (BufferedInputStream bis = new BufferedInputStream(
                                new FileInputStream(file.toFile()), BackupConfig.getBufferSize())) {
                            byte[] buffer = new byte[BackupConfig.getBufferSize()];
                            int len;
                            while ((len = bis.read(buffer)) > 0) {
                                zipOut.write(buffer, 0, len);
                            }
                        }

                        zipOut.closeEntry();
                        processedFiles.add(relativePath);

                        return FileVisitResult.CONTINUE;
                    }
                });

                // Add a manifest of included files
                ZipEntry manifestEntry = new ZipEntry("file_manifest.txt");
                zipOut.putNextEntry(manifestEntry);

                try (PrintWriter pw = new PrintWriter(new OutputStreamWriter(zipOut))) {
                    for (Path path : processedFiles) {
                        pw.println(path.toString());
                    }
                }

                zipOut.closeEntry();
            }

            // Verify the backup if enabled
            if (BackupConfig.isVerifyBackupsEnabled()) {
                if (!verifyBackup(backupFile)) {
                    Pokecobbleclaim.LOGGER.error("Backup verification failed for player " + playerUuid);
                    backupFile.delete(); // Delete the corrupted backup
                    return null;
                }
            }

            Pokecobbleclaim.LOGGER.info("Created backup for player " + playerUuid + ": " + backupFile.getPath());
            return backupFile.getPath();
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to backup player data for " + playerUuid + ": " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * Restores player data from a backup file.
     *
     * @param backupFilePath The path to the backup file
     * @return true if restore was successful, false otherwise
     */
    public static boolean restoreFromBackup(String backupFilePath) {
        try {
            File backupFile = new File(backupFilePath);
            if (!backupFile.exists() || !backupFile.isFile()) {
                Pokecobbleclaim.LOGGER.error("Backup file not found: " + backupFilePath);
                return false;
            }

            // Verify the backup first
            if (BackupConfig.isVerifyBackupsEnabled() && !verifyBackup(backupFile)) {
                Pokecobbleclaim.LOGGER.error("Backup verification failed, cannot restore: " + backupFilePath);
                return false;
            }

            // Get the players directory
            File playersDir = getPlayersDirectory();

            // Open the ZIP file
            try (ZipFile zipFile = new ZipFile(backupFile)) {
                // Check if this is an incremental or differential backup
                ZipEntry metadataEntry = zipFile.getEntry("backup_info.properties");
                if (metadataEntry != null) {
                    Properties backupInfo = new Properties();
                    try (InputStream is = zipFile.getInputStream(metadataEntry)) {
                        backupInfo.load(is);
                    }

                    String backupType = backupInfo.getProperty("backup_type");
                    String baseBackup = backupInfo.getProperty("base_backup", "");

                    // If this is an incremental or differential backup, we need to restore the base first
                    if ((BACKUP_TYPE_INCREMENTAL.equals(backupType) || BACKUP_TYPE_DIFFERENTIAL.equals(backupType))
                            && !baseBackup.isEmpty()) {
                        File baseBackupFile = new File(getBackupsDirectory(), baseBackup);
                        if (baseBackupFile.exists()) {
                            Pokecobbleclaim.LOGGER.info("Restoring base backup first: " + baseBackup);
                            if (!restoreFromBackup(baseBackupFile.getPath())) {
                                Pokecobbleclaim.LOGGER.error("Failed to restore base backup: " + baseBackup);
                                return false;
                            }
                        } else {
                            Pokecobbleclaim.LOGGER.warn("Base backup not found: " + baseBackup + ", continuing with incremental only");
                        }
                    }
                }

                // Get the list of files to restore from the manifest
                List<String> filesToRestore = new ArrayList<>();
                ZipEntry manifestEntry = zipFile.getEntry("file_manifest.txt");
                if (manifestEntry != null) {
                    try (BufferedReader reader = new BufferedReader(new InputStreamReader(zipFile.getInputStream(manifestEntry)))) {
                        String line;
                        while ((line = reader.readLine()) != null) {
                            filesToRestore.add(line);
                        }
                    }
                } else {
                    // No manifest, restore all files except metadata and manifest
                    Enumeration<? extends ZipEntry> entries = zipFile.entries();
                    while (entries.hasMoreElements()) {
                        ZipEntry entry = entries.nextElement();
                        String name = entry.getName();
                        if (!entry.isDirectory() && !name.equals("backup_info.properties") && !name.equals("file_manifest.txt")) {
                            filesToRestore.add(name);
                        }
                    }
                }

                // Restore each file
                for (String filePath : filesToRestore) {
                    ZipEntry entry = zipFile.getEntry(filePath);
                    if (entry == null) {
                        Pokecobbleclaim.LOGGER.warn("File not found in backup: " + filePath);
                        continue;
                    }

                    File entryFile = new File(playersDir, filePath);

                    // Create parent directories if they don't exist
                    if (entryFile.getParentFile() != null) {
                        entryFile.getParentFile().mkdirs();
                    }

                    // Extract file with buffering
                    try (BufferedInputStream bis = new BufferedInputStream(zipFile.getInputStream(entry), BackupConfig.getBufferSize());
                         BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(entryFile), BackupConfig.getBufferSize())) {
                        byte[] buffer = new byte[BackupConfig.getBufferSize()];
                        int len;
                        while ((len = bis.read(buffer)) > 0) {
                            bos.write(buffer, 0, len);
                        }
                    }
                }
            }

            // Clear the file hash cache to force recalculation on next backup
            fileHashes.clear();

            Pokecobbleclaim.LOGGER.info("Restored player data from backup: " + backupFilePath);
            return true;
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to restore from backup: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * Compresses a file using GZIP.
     *
     * @param inputFile The file to compress
     * @return The compressed file, or null if compression failed
     */
    public static File compressFile(File inputFile) {
        if (!inputFile.exists() || !inputFile.isFile()) {
            Pokecobbleclaim.LOGGER.warn("File not found for compression: " + inputFile.getPath());
            return null;
        }

        File compressedFile = new File(inputFile.getPath() + ".gz");

        try (BufferedInputStream bis = new BufferedInputStream(new FileInputStream(inputFile), BackupConfig.getBufferSize());
             BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(compressedFile), BackupConfig.getBufferSize());
             GZIPOutputStream gzipOut = new GZIPOutputStream(bos) {
                 {
                     // Set the compression level using reflection
                     try {
                         java.lang.reflect.Field field = GZIPOutputStream.class.getDeclaredField("def");
                         field.setAccessible(true);
                         Deflater deflater = (Deflater) field.get(this);
                         deflater.setLevel(BackupConfig.getCompressionLevel());
                     } catch (Exception e) {
                         // Fallback to default compression level if reflection fails
                         Pokecobbleclaim.LOGGER.warn("Failed to set compression level: " + e.getMessage());
                     }
                 }
             }) {

            byte[] buffer = new byte[BackupConfig.getBufferSize()];
            int len;
            while ((len = bis.read(buffer)) > 0) {
                gzipOut.write(buffer, 0, len);
            }

            Pokecobbleclaim.LOGGER.debug("Compressed file: " + inputFile.getPath());
            return compressedFile;
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to compress file: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * Decompresses a GZIP file.
     *
     * @param compressedFile The compressed file
     * @return The decompressed file, or null if decompression failed
     */
    public static File decompressFile(File compressedFile) {
        if (!compressedFile.exists() || !compressedFile.isFile() || !compressedFile.getName().endsWith(".gz")) {
            Pokecobbleclaim.LOGGER.warn("Invalid compressed file: " + compressedFile.getPath());
            return null;
        }

        String outputPath = compressedFile.getPath().substring(0, compressedFile.getPath().length() - 3);
        File outputFile = new File(outputPath);

        try (BufferedInputStream bis = new BufferedInputStream(new FileInputStream(compressedFile), BackupConfig.getBufferSize());
             GZIPInputStream gzipIn = new GZIPInputStream(bis, BackupConfig.getBufferSize());
             BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(outputFile), BackupConfig.getBufferSize())) {

            byte[] buffer = new byte[BackupConfig.getBufferSize()];
            int len;
            while ((len = gzipIn.read(buffer)) > 0) {
                bos.write(buffer, 0, len);
            }

            Pokecobbleclaim.LOGGER.debug("Decompressed file: " + compressedFile.getPath());
            return outputFile;
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to decompress file: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * Gets the players directory.
     *
     * @return The players directory
     */
    public static File getPlayersDirectory() {
        File dataDir = new File(FabricLoader.getInstance().getGameDir().toFile(), DATA_FOLDER);
        return new File(dataDir, PLAYERS_FOLDER);
    }

    /**
     * Gets the backups directory.
     *
     * @return The backups directory
     */
    public static File getBackupsDirectory() {
        File dataDir = new File(FabricLoader.getInstance().getGameDir().toFile(), DATA_FOLDER);
        return new File(dataDir, BACKUPS_FOLDER);
    }

    /**
     * Cleans up old backups, keeping only the specified number of most recent backups.
     *
     * @param maxBackups The maximum number of backups to keep
     * @return The number of backups deleted
     */
    public static int cleanupOldBackups(int maxBackups) {
        try {
            File backupsDir = getBackupsDirectory();
            if (!backupsDir.exists() || !backupsDir.isDirectory()) {
                return 0;
            }

            // Get all backup files
            File[] backupFiles = backupsDir.listFiles((dir, name) -> name.startsWith("player_data_") && name.endsWith(".zip"));
            if (backupFiles == null || backupFiles.length <= maxBackups) {
                return 0;
            }

            // Sort by last modified time (oldest first)
            java.util.Arrays.sort(backupFiles, Comparator.comparingLong(File::lastModified));

            // Delete oldest backups
            int deleteCount = backupFiles.length - maxBackups;
            for (int i = 0; i < deleteCount; i++) {
                if (backupFiles[i].delete()) {
                    Pokecobbleclaim.LOGGER.info("Deleted old backup: " + backupFiles[i].getName());
                } else {
                    Pokecobbleclaim.LOGGER.warn("Failed to delete old backup: " + backupFiles[i].getName());
                }
            }

            return deleteCount;
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to cleanup old backups: " + e.getMessage());
            e.printStackTrace();
            return 0;
        }
    }

    /**
     * Shuts down the backup executor service.
     * This should be called during server shutdown to ensure proper cleanup.
     */
    public static void shutdownBackupExecutor() {
        BACKUP_EXECUTOR.shutdown();
        try {
            if (!BACKUP_EXECUTOR.awaitTermination(5, java.util.concurrent.TimeUnit.SECONDS)) {
                BACKUP_EXECUTOR.shutdownNow();
            }
        } catch (InterruptedException e) {
            BACKUP_EXECUTOR.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}

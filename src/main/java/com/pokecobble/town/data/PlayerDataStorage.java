package com.pokecobble.town.data;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonSyntaxException;
import com.google.gson.reflect.TypeToken;
import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.TownPlayer;
import com.pokecobble.town.TownPlayerRank;
import com.pokecobble.town.config.BackupConfig;
import net.fabricmc.loader.api.FabricLoader;

import java.io.*;
import java.lang.reflect.Type;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

/**
 * Handles saving and loading player data to/from disk.
 * This class manages individual player data files, storing them in a folder structure
 * organized by player UUID.
 */
public class PlayerDataStorage {
    // Constants for file paths
    private static final String DATA_FOLDER = "pokecobbleclaim";
    private static final String PLAYERS_FOLDER = "players";
    private static final String PLAYER_DATA_FILE = "player_data.json";
    private static final String COMPRESSED_PLAYER_DATA_FILE = "player_data.json.gz";

    // GSON instance for serialization
    private static final Gson GSON = new GsonBuilder().setPrettyPrinting().create();

    // Cache of loaded player data with a maximum size
    private static final Map<UUID, CachedPlayerData> playerDataCache = Collections.synchronizedMap(
            new LinkedHashMap<UUID, CachedPlayerData>(16, 0.75f, true) {
                private static final int MAX_CACHE_SIZE = 100;

                @Override
                protected boolean removeEldestEntry(Map.Entry<UUID, CachedPlayerData> eldest) {
                    return size() > MAX_CACHE_SIZE;
                }
            });

    // Lock for file operations
    private static final ReadWriteLock fileLock = new ReentrantReadWriteLock();

    // Thread pool for async file operations
    private static final ExecutorService fileIOExecutor = Executors.newFixedThreadPool(2, r -> {
        Thread t = new Thread(r, "PlayerDataIO");
        t.setDaemon(true);
        return t;
    });

    // Flag to track if shutdown hook is registered
    private static boolean shutdownHookRegistered = false;

    /**
     * Initializes the player data storage system.
     * Registers a shutdown hook to save all dirty data.
     */
    public static void initialize() {
        if (!shutdownHookRegistered) {
            // Register shutdown hook to save all dirty data
            Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                Pokecobbleclaim.LOGGER.info("Saving all dirty player data before shutdown");
                saveAllDirtyData();
                fileIOExecutor.shutdown();
            }, "PlayerDataShutdownHook"));

            shutdownHookRegistered = true;
            Pokecobbleclaim.LOGGER.info("Player data storage system initialized");
        }
    }

    /**
     * Saves player data to disk.
     *
     * @param playerId The UUID of the player
     * @param townPlayer The TownPlayer object containing player data
     * @param townId The UUID of the town the player belongs to (can be null)
     */
    public static void savePlayerData(UUID playerId, TownPlayer townPlayer, UUID townId) {
        // Load existing data to preserve timestamps
        SerializablePlayerData existingData = loadPlayerData(playerId);
        SerializablePlayerData playerData;

        if (existingData != null) {
            // Create new data object but preserve existing timestamps
            playerData = new SerializablePlayerData(playerId, townPlayer, townId);

            // Preserve existing timestamps (don't overwrite with current time)
            playerData.lastSeenTime = existingData.getLastSeenTime();
            playerData.townJoinTime = existingData.getTownJoinTime();
            playerData.setTotalPlayTime(existingData.getTotalPlayTime());

            // Only update lastLogin if it's actually a new login (not just a data save)
            // The lastLogin should only be updated in PlayerDataManager.onPlayerJoin
        } else {
            // Create new player data object
            playerData = new SerializablePlayerData(playerId, townPlayer, townId);
        }

        // Update cache with dirty flag
        synchronized (playerDataCache) {
            CachedPlayerData cachedData = playerDataCache.get(playerId);
            if (cachedData != null) {
                // Update existing cached data
                cachedData.markDirty();
            } else {
                // Create new cached data
                cachedData = new CachedPlayerData(playerData);
                cachedData.markDirty();
                playerDataCache.put(playerId, cachedData);
            }
        }

        // Schedule async save
        CompletableFuture.runAsync(() -> savePlayerDataToDisk(playerId), fileIOExecutor)
            .exceptionally(e -> {
                Pokecobbleclaim.LOGGER.error("Failed to save player data for " + playerId + ": " + e.getMessage());
                e.printStackTrace();
                return null;
            });
    }

    /**
     * Saves player data to disk synchronously.
     *
     * @param playerId The UUID of the player
     */
    private static void savePlayerDataToDisk(UUID playerId) {
        // Acquire write lock
        fileLock.writeLock().lock();

        try {
            // Get cached data
            CachedPlayerData cachedData;
            synchronized (playerDataCache) {
                cachedData = playerDataCache.get(playerId);
                if (cachedData == null || !cachedData.isDirty()) {
                    return; // Nothing to save
                }
            }

            SerializablePlayerData playerData = cachedData.getData();

            // Create player directory if it doesn't exist
            File playerDir = getPlayerDirectory(playerId);
            if (!playerDir.exists()) {
                playerDir.mkdirs();
            }

            // Determine whether to use compression
            boolean useCompression = BackupConfig.isCompressionEnabled();

            // Use atomic file operations to prevent data corruption
            if (useCompression) {
                // Create temp file for compressed data
                File tempFile = File.createTempFile("player_data", ".tmp", playerDir);
                File compressedFile = new File(playerDir, COMPRESSED_PLAYER_DATA_FILE);

                // Write to temp file first
                try (Writer writer = new OutputStreamWriter(
                        new BufferedOutputStream(
                            new GZIPOutputStream(
                                new FileOutputStream(tempFile), BackupConfig.getCompressionLevel()),
                            BackupConfig.getBufferSize()))) {
                    GSON.toJson(playerData, writer);
                }

                // Atomically replace the real file with the temp file
                Files.move(tempFile.toPath(), compressedFile.toPath(), StandardCopyOption.REPLACE_EXISTING, StandardCopyOption.ATOMIC_MOVE);

                // Remove uncompressed file if it exists
                File uncompressedFile = new File(playerDir, PLAYER_DATA_FILE);
                if (uncompressedFile.exists()) {
                    uncompressedFile.delete();
                }

                Pokecobbleclaim.LOGGER.debug("Saved compressed player data for " + playerId);
            } else {
                // Create temp file for uncompressed data
                File tempFile = File.createTempFile("player_data", ".tmp", playerDir);
                File playerDataFile = new File(playerDir, PLAYER_DATA_FILE);

                // Write to temp file first
                try (Writer writer = new BufferedWriter(
                        new FileWriter(tempFile), BackupConfig.getBufferSize())) {
                    GSON.toJson(playerData, writer);
                }

                // Atomically replace the real file with the temp file
                Files.move(tempFile.toPath(), playerDataFile.toPath(), StandardCopyOption.REPLACE_EXISTING, StandardCopyOption.ATOMIC_MOVE);

                // Remove compressed file if it exists
                File compressedFile = new File(playerDir, COMPRESSED_PLAYER_DATA_FILE);
                if (compressedFile.exists()) {
                    compressedFile.delete();
                }

                Pokecobbleclaim.LOGGER.debug("Saved player data for " + playerId);
            }

            // Clear dirty flag
            synchronized (playerDataCache) {
                CachedPlayerData updatedCache = playerDataCache.get(playerId);
                if (updatedCache != null) {
                    updatedCache.clearDirty();
                }
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to save player data for " + playerId + ": " + e.getMessage());
            e.printStackTrace();
        } finally {
            // Release write lock
            fileLock.writeLock().unlock();
        }
    }

    /**
     * Saves all dirty player data to disk.
     */
    public static void saveAllDirtyData() {
        List<UUID> dirtyPlayers = new ArrayList<>();

        // Collect all dirty players
        synchronized (playerDataCache) {
            for (Map.Entry<UUID, CachedPlayerData> entry : playerDataCache.entrySet()) {
                if (entry.getValue().isDirty()) {
                    dirtyPlayers.add(entry.getKey());
                }
            }
        }

        // Save each dirty player
        for (UUID playerId : dirtyPlayers) {
            try {
                savePlayerDataToDisk(playerId);
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Failed to save dirty data for player " + playerId + ": " + e.getMessage());
            }
        }

        Pokecobbleclaim.LOGGER.info("Saved " + dirtyPlayers.size() + " dirty player data files");
    }

    /**
     * Loads player data from disk.
     *
     * @param playerId The UUID of the player
     * @return The loaded player data, or null if no data exists
     */
    public static SerializablePlayerData loadPlayerData(UUID playerId) {
        // Check cache first
        synchronized (playerDataCache) {
            CachedPlayerData cachedData = playerDataCache.get(playerId);
            if (cachedData != null) {
                return cachedData.getData();
            }
        }

        // Acquire read lock
        fileLock.readLock().lock();

        try {
            // Get player directory
            File playerDir = getPlayerDirectory(playerId);
            if (!playerDir.exists()) {
                return null; // No data exists
            }

            // Check for compressed file first
            File compressedFile = new File(playerDir, COMPRESSED_PLAYER_DATA_FILE);
            File uncompressedFile = new File(playerDir, PLAYER_DATA_FILE);

            SerializablePlayerData playerData = null;

            // Try to load from compressed file first
            if (compressedFile.exists()) {
                try (Reader reader = new BufferedReader(
                        new InputStreamReader(
                            new GZIPInputStream(
                                new BufferedInputStream(
                                    new FileInputStream(compressedFile),
                                    BackupConfig.getBufferSize()
                                )
                            )
                        ),
                        BackupConfig.getBufferSize()
                    )) {
                    playerData = GSON.fromJson(reader, SerializablePlayerData.class);
                    validatePlayerData(playerData);
                    Pokecobbleclaim.LOGGER.debug("Loaded compressed player data for " + playerId);
                } catch (JsonSyntaxException e) {
                    Pokecobbleclaim.LOGGER.error("Corrupted compressed player data for " + playerId + ": " + e.getMessage());

                    // Try to restore from backup
                    playerData = restoreFromBackup(playerId);
                    if (playerData == null) {
                        // If no backup, try uncompressed file as fallback
                        if (uncompressedFile.exists()) {
                            try (Reader reader = new BufferedReader(
                                    new FileReader(uncompressedFile),
                                    BackupConfig.getBufferSize())) {
                                playerData = GSON.fromJson(reader, SerializablePlayerData.class);
                                validatePlayerData(playerData);
                                Pokecobbleclaim.LOGGER.info("Recovered player data from uncompressed file for " + playerId);
                            } catch (Exception ex) {
                                Pokecobbleclaim.LOGGER.error("Failed to recover player data from uncompressed file: " + ex.getMessage());
                            }
                        }
                    }
                }
            }
            // If compressed file doesn't exist or failed to load, try uncompressed file
            else if (uncompressedFile.exists()) {
                try (Reader reader = new BufferedReader(
                        new FileReader(uncompressedFile),
                        BackupConfig.getBufferSize())) {
                    playerData = GSON.fromJson(reader, SerializablePlayerData.class);
                    validatePlayerData(playerData);
                    Pokecobbleclaim.LOGGER.debug("Loaded player data for " + playerId);
                } catch (JsonSyntaxException e) {
                    Pokecobbleclaim.LOGGER.error("Corrupted player data for " + playerId + ": " + e.getMessage());

                    // Try to restore from backup
                    playerData = restoreFromBackup(playerId);
                }
            } else {
                return null; // No data exists
            }

            // Update cache
            if (playerData != null) {
                synchronized (playerDataCache) {
                    playerDataCache.put(playerId, new CachedPlayerData(playerData));
                }
            }

            return playerData;
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to load player data for " + playerId + ": " + e.getMessage());
            e.printStackTrace();
            return null;
        } finally {
            // Release read lock
            fileLock.readLock().unlock();
        }
    }

    /**
     * Validates player data to ensure it's not corrupted.
     *
     * @param playerData The player data to validate
     * @throws IllegalArgumentException if the player data is invalid
     */
    private static void validatePlayerData(SerializablePlayerData playerData) {
        if (playerData == null) {
            throw new IllegalArgumentException("Player data is null");
        }

        if (playerData.getPlayerId() == null) {
            throw new IllegalArgumentException("Player ID is null");
        }

        if (playerData.getPlayerName() == null || playerData.getPlayerName().isEmpty()) {
            throw new IllegalArgumentException("Player name is null or empty");
        }

        // Validate rank
        try {
            TownPlayerRank rank = playerData.getRank();
            if (rank == null) {
                throw new IllegalArgumentException("Player rank is null");
            }
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Invalid player rank: " + e.getMessage());
        }

        // Migrate missing timestamp fields for existing player data
        migratePlayerDataTimestamps(playerData);
    }

    /**
     * Migrates player data to add missing timestamp fields for existing players.
     *
     * @param playerData The player data to migrate
     */
    private static void migratePlayerDataTimestamps(SerializablePlayerData playerData) {
        boolean needsMigration = false;

        // Check if lastSeenTime is missing (0 or not set)
        if (playerData.lastSeenTime <= 0) {
            // Set to lastLogin as a reasonable default
            playerData.lastSeenTime = playerData.getLastLogin();
            needsMigration = true;
        }

        // Check if townJoinTime is missing (0 or not set)
        if (playerData.townJoinTime <= 0) {
            // Set to lastLogin as a reasonable default (not perfect but better than nothing)
            playerData.townJoinTime = playerData.getLastLogin();
            needsMigration = true;
        }

        if (needsMigration) {
            Pokecobbleclaim.LOGGER.debug("Migrated timestamp fields for player " + playerData.getPlayerId());
        }
    }

    /**
     * Attempts to restore player data from a backup.
     *
     * @param playerId The UUID of the player
     * @return The restored player data, or null if restoration failed
     */
    private static SerializablePlayerData restoreFromBackup(UUID playerId) {
        Pokecobbleclaim.LOGGER.info("Attempting to restore player data from backup for " + playerId);

        try {
            // Get player backup directory
            File backupsDir = new File(PlayerDataUtils.getBackupsDirectory(), playerId.toString());
            if (!backupsDir.exists() || !backupsDir.isDirectory()) {
                Pokecobbleclaim.LOGGER.warn("No backups found for player " + playerId);
                return null;
            }

            // Get all backup files
            File[] backupFiles = backupsDir.listFiles((dir, name) -> name.startsWith("player_data_") && name.endsWith(".zip"));
            if (backupFiles == null || backupFiles.length == 0) {
                Pokecobbleclaim.LOGGER.warn("No backup files found for player " + playerId);
                return null;
            }

            // Sort by last modified time (newest first)
            Arrays.sort(backupFiles, Comparator.comparingLong(File::lastModified).reversed());

            // Try to restore from newest backup
            for (File backupFile : backupFiles) {
                try {
                    if (PlayerDataUtils.restoreFromBackup(backupFile.getPath())) {
                        // Reload player data after restoration
                        File playerDir = getPlayerDirectory(playerId);
                        File compressedFile = new File(playerDir, COMPRESSED_PLAYER_DATA_FILE);
                        File uncompressedFile = new File(playerDir, PLAYER_DATA_FILE);

                        SerializablePlayerData playerData = null;

                        // Try compressed file first
                        if (compressedFile.exists()) {
                            try (Reader reader = new BufferedReader(
                                    new InputStreamReader(
                                        new GZIPInputStream(
                                            new BufferedInputStream(
                                                new FileInputStream(compressedFile),
                                                BackupConfig.getBufferSize()
                                            )
                                        )
                                    ),
                                    BackupConfig.getBufferSize()
                                )) {
                                playerData = GSON.fromJson(reader, SerializablePlayerData.class);
                                validatePlayerData(playerData);
                            }
                        }
                        // Then try uncompressed file
                        else if (uncompressedFile.exists()) {
                            try (Reader reader = new BufferedReader(
                                    new FileReader(uncompressedFile),
                                    BackupConfig.getBufferSize())) {
                                playerData = GSON.fromJson(reader, SerializablePlayerData.class);
                                validatePlayerData(playerData);
                            }
                        }

                        if (playerData != null) {
                            Pokecobbleclaim.LOGGER.info("Successfully restored player data from backup for " + playerId);
                            return playerData;
                        }
                    }
                } catch (Exception e) {
                    Pokecobbleclaim.LOGGER.error("Failed to restore from backup " + backupFile.getName() + ": " + e.getMessage());
                }
            }

            Pokecobbleclaim.LOGGER.warn("All backup restoration attempts failed for player " + playerId);
            return null;
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to restore player data from backup: " + e.getMessage());
            return null;
        }
    }

    /**
     * Gets the directory for a player's data.
     *
     * @param playerId The UUID of the player
     * @return The player's data directory
     */
    private static File getPlayerDirectory(UUID playerId) {
        // Create base data directory if it doesn't exist
        File dataDir = new File(FabricLoader.getInstance().getGameDir().toFile(), DATA_FOLDER);
        if (!dataDir.exists()) {
            dataDir.mkdirs();
        }

        // Create players directory if it doesn't exist
        File playersDir = new File(dataDir, PLAYERS_FOLDER);
        if (!playersDir.exists()) {
            playersDir.mkdirs();
        }

        // Return player-specific directory
        return new File(playersDir, playerId.toString());
    }

    /**
     * Clears the player data cache.
     */
    public static void clearCache() {
        playerDataCache.clear();
    }

    /**
     * Removes a player's data from the cache.
     *
     * @param playerId The UUID of the player
     */
    public static void removeFromCache(UUID playerId) {
        playerDataCache.remove(playerId);
    }

    /**
     * Wrapper class for cached player data with dirty tracking.
     */
    private static class CachedPlayerData {
        private final SerializablePlayerData data;
        private boolean dirty;
        private long lastAccessed;

        public CachedPlayerData(SerializablePlayerData data) {
            this.data = data;
            this.dirty = false;
            this.lastAccessed = System.currentTimeMillis();
        }

        public SerializablePlayerData getData() {
            this.lastAccessed = System.currentTimeMillis();
            return data;
        }

        public void markDirty() {
            this.dirty = true;
        }

        public boolean isDirty() {
            return dirty;
        }

        public void clearDirty() {
            this.dirty = false;
        }

        public long getLastAccessed() {
            return lastAccessed;
        }
    }

    /**
     * A serializable version of player data.
     */
    public static class SerializablePlayerData {
        private String playerId;
        private String playerName;
        private String townId; // Can be null if player is not in a town
        private String rank;
        private Map<String, Map<String, Boolean>> permissions;
        private long lastLogin;
        private long lastSeenTime; // When player was last online (different from lastLogin)
        private long townJoinTime; // When player first joined their current town
        private long totalPlayTime;
        private int dataVersion;

        public SerializablePlayerData() {
            // Default constructor for GSON
        }

        public SerializablePlayerData(UUID playerId, TownPlayer townPlayer, UUID townId) {
            this.playerId = playerId.toString();
            this.playerName = townPlayer.getName();
            this.townId = townId != null ? townId.toString() : null;
            this.rank = townPlayer.getRank().name();
            this.permissions = new HashMap<>(townPlayer.getAllPermissions());
            this.lastLogin = System.currentTimeMillis();
            this.lastSeenTime = System.currentTimeMillis(); // Initialize to current time
            this.townJoinTime = System.currentTimeMillis(); // Will be set properly when joining town
            this.dataVersion = townPlayer.getDataVersion();
        }

        /**
         * Gets the player's UUID.
         *
         * @return The player's UUID
         */
        public UUID getPlayerId() {
            return UUID.fromString(playerId);
        }

        /**
         * Gets the player's name.
         *
         * @return The player's name
         */
        public String getPlayerName() {
            return playerName;
        }

        /**
         * Gets the town's UUID.
         *
         * @return The town's UUID, or null if the player is not in a town
         */
        public UUID getTownId() {
            return townId != null ? UUID.fromString(townId) : null;
        }

        /**
         * Gets the player's rank.
         *
         * @return The player's rank
         */
        public TownPlayerRank getRank() {
            return TownPlayerRank.valueOf(rank);
        }

        /**
         * Gets the player's permissions.
         *
         * @return The player's permissions
         */
        public Map<String, Map<String, Boolean>> getPermissions() {
            return permissions;
        }

        /**
         * Gets the player's last login time.
         *
         * @return The player's last login time
         */
        public long getLastLogin() {
            return lastLogin;
        }

        /**
         * Gets the player's last seen time.
         *
         * @return The player's last seen time
         */
        public long getLastSeenTime() {
            return lastSeenTime;
        }

        /**
         * Sets the player's last seen time.
         *
         * @param lastSeenTime The player's last seen time
         */
        public void setLastSeenTime(long lastSeenTime) {
            this.lastSeenTime = lastSeenTime;
        }

        /**
         * Gets the player's town join time.
         *
         * @return The player's town join time
         */
        public long getTownJoinTime() {
            return townJoinTime;
        }

        /**
         * Sets the player's town join time.
         *
         * @param townJoinTime The player's town join time
         */
        public void setTownJoinTime(long townJoinTime) {
            this.townJoinTime = townJoinTime;
        }

        /**
         * Gets the player's total play time.
         *
         * @return The player's total play time
         */
        public long getTotalPlayTime() {
            return totalPlayTime;
        }

        /**
         * Sets the player's total play time.
         *
         * @param totalPlayTime The player's total play time
         */
        public void setTotalPlayTime(long totalPlayTime) {
            this.totalPlayTime = totalPlayTime;
        }

        /**
         * Gets the player's data version.
         *
         * @return The player's data version
         */
        public int getDataVersion() {
            return dataVersion;
        }

        /**
         * Updates the player's last login time.
         */
        public void updateLastLogin() {
            this.lastLogin = System.currentTimeMillis();
        }

        /**
         * Updates the player's last seen time.
         */
        public void updateLastSeen() {
            this.lastSeenTime = System.currentTimeMillis();
        }

        /**
         * Creates a TownPlayer object from this serializable data.
         *
         * @return A new TownPlayer object
         */
        public TownPlayer toTownPlayer() {
            UUID id = UUID.fromString(playerId);
            TownPlayerRank playerRank = TownPlayerRank.valueOf(rank);
            TownPlayer townPlayer = new TownPlayer(id, playerName, playerRank);

            // Set permissions
            for (Map.Entry<String, Map<String, Boolean>> entry : permissions.entrySet()) {
                townPlayer.setCategoryPermissions(entry.getKey(), entry.getValue());
            }

            return townPlayer;
        }
    }
}

package com.pokecobble.town;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.election.Election;
import com.pokecobble.town.election.ElectionManager;
import com.pokecobble.town.network.town.TownDataSynchronizer;
import com.pokecobble.town.network.NetworkConstants;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.text.Text;

import java.util.*;

/**
 * Manages towns in the game.
 */
public class TownManager {
    private static final TownManager INSTANCE = new TownManager();
    private final Map<UUID, Town> towns = new HashMap<>();
    private final Map<UUID, UUID> playerTowns = new HashMap<>();

    // Server instance for synchronization
    private MinecraftServer server;



    private TownManager() {
        // Private constructor for singleton
        // Note: Town data loading is handled by the main mod initialization
        // Dummy towns will be created later if needed, after real town loading is attempted
    }

    /**
     * Gets the singleton instance of the TownManager.
     *
     * @return The TownManager instance
     */
    public static TownManager getInstance() {
        return INSTANCE;
    }



    /**
     * Gets the name of a player by their UUID.
     * Checks multiple data sources to find the player's name, including online players,
     * cached player data, town membership data, and persistent storage.
     *
     * @param playerId The player's UUID
     * @return The player's name or "Unknown Player"
     */
    public String getPlayerName(UUID playerId) {
        // First, check if player is currently online
        if (server != null) {
            ServerPlayerEntity player = server.getPlayerManager().getPlayer(playerId);
            if (player != null) {
                return player.getName().getString();
            }
        }

        // Second, try to get name from PlayerDataManager (cached data)
        TownPlayer townPlayer = PlayerDataManager.getInstance().getPlayer(playerId);
        if (townPlayer != null && townPlayer.getName() != null && !townPlayer.getName().isEmpty()) {
            return townPlayer.getName();
        }

        // Third, check if player is in any town and get name from town data
        for (Town town : towns.values()) {
            TownPlayer player = town.getPlayer(playerId);
            if (player != null && player.getName() != null && !player.getName().isEmpty()) {
                return player.getName();
            }
        }

        // Fourth, try to load from persistent storage
        try {
            com.pokecobble.town.data.PlayerDataStorage.SerializablePlayerData playerData =
                com.pokecobble.town.data.PlayerDataStorage.loadPlayerData(playerId);
            if (playerData != null && playerData.getPlayerName() != null && !playerData.getPlayerName().isEmpty()) {
                return playerData.getPlayerName();
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.debug("Could not load player data for " + playerId + " when getting player name");
        }

        return "Unknown Player";
    }





    /**
     * Checks if a town name is already in use.
     *
     * @param name The name to check
     * @return true if the name is already in use, false otherwise
     */
    public boolean isTownNameTaken(String name) {
        for (Town town : towns.values()) {
            if (town.getName().equalsIgnoreCase(name)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Creates a new town with the given name.
     *
     * @param name The name of the town
     * @return The created town, or null if a town with that name already exists
     */
    public Town createTown(String name) {
        // Check if a town with this name already exists
        if (isTownNameTaken(name)) {
            return null;
        }

        Town town = new Town(name);
        towns.put(town.getId(), town);

        // Save the town to disk
        com.pokecobble.town.data.TownDataStorage.saveTown(town);

        // Synchronize town data and broadcast to all players
        if (server != null) {
            TownDataSynchronizer.syncTownData(server, town);
            // Broadcast the updated town list to all players
            TownDataSynchronizer.broadcastTownListUpdate(server);
        }

        Pokecobbleclaim.LOGGER.info("Created new town: " + name);
        return town;
    }

    /**
     * Creates a new town with the given name and assigns a player as the mayor.
     *
     * @param name The name of the town
     * @param mayorId The UUID of the player who will be the mayor
     * @return The created town, or null if a town with that name already exists or player is already in a town
     */
    public Town createTownWithMayor(String name, UUID mayorId) {
        // Check if player is already in a town
        if (playerTowns.containsKey(mayorId)) {
            return null;
        }

        // Check if a town with this name already exists
        if (isTownNameTaken(name)) {
            return null;
        }

        Town town = new Town(name);
        towns.put(town.getId(), town);

        // Add the mayor to the town with OWNER rank
        TownPlayer mayorPlayer = new TownPlayer(mayorId, "Unknown", TownPlayerRank.OWNER);
        town.addPlayer(mayorPlayer);
        playerTowns.put(mayorId, town.getId());

        // Handle the membership change with centralized data saving
        handlePlayerTownMembershipChange(mayorId, null, town.getId(), "created and became mayor of town " + name);

        // Synchronize town data and broadcast to all players
        if (server != null) {
            TownDataSynchronizer.syncTownData(server, town);
            // Broadcast the updated town list to all players
            TownDataSynchronizer.broadcastTownListUpdate(server);
        }
        return town;
    }

    /**
     * Creates a new town with the given parameters.
     *
     * @param name The name of the town
     * @param description The town description
     * @param joinType The join type for the town
     * @param maxPlayers The maximum number of players allowed
     * @return The created town, or null if a town with that name already exists
     */
    public Town createTown(String name, String description, Town.JoinType joinType, int maxPlayers) {
        // Check if a town with this name already exists
        if (isTownNameTaken(name)) {
            return null;
        }

        Town town = new Town(name, description, joinType, maxPlayers);
        towns.put(town.getId(), town);

        // Save the town to disk
        com.pokecobble.town.data.TownDataStorage.saveTown(town);

        // Synchronize town data and broadcast to all players
        if (server != null) {
            TownDataSynchronizer.syncTownData(server, town);
            // Broadcast the updated town list to all players
            TownDataSynchronizer.broadcastTownListUpdate(server);
        }

        Pokecobbleclaim.LOGGER.info("Created new town: " + name + " with custom settings");
        return town;
    }

    /**
     * Creates a new town with the given parameters and assigns a player as the mayor.
     *
     * @param name The name of the town
     * @param description The town description
     * @param joinType The join type for the town
     * @param maxPlayers The maximum number of players allowed
     * @param mayorId The UUID of the player who will be the mayor
     * @return The created town, or null if a town with that name already exists or player is already in a town
     */
    public Town createTownWithMayor(String name, String description, Town.JoinType joinType, int maxPlayers, UUID mayorId) {
        // Check if player is already in a town
        if (playerTowns.containsKey(mayorId)) {
            return null;
        }

        // Check if a town with this name already exists
        if (isTownNameTaken(name)) {
            return null;
        }

        Town town = new Town(name, description, joinType, maxPlayers);
        towns.put(town.getId(), town);

        // Add the mayor to the town with OWNER rank
        TownPlayer mayorPlayer = new TownPlayer(mayorId, "Unknown", TownPlayerRank.OWNER);
        town.addPlayer(mayorPlayer);
        playerTowns.put(mayorId, town.getId());

        // Handle the membership change with centralized data saving
        handlePlayerTownMembershipChange(mayorId, null, town.getId(), "created and became mayor of town " + name);

        // Synchronize town data and broadcast to all players
        if (server != null) {
            TownDataSynchronizer.syncTownData(server, town);
            // Broadcast the updated town list to all players
            TownDataSynchronizer.broadcastTownListUpdate(server);
        }
        return town;
    }

    /**
     * Creates a new town with the given parameters (legacy method).
     *
     * @param name The name of the town
     * @param description The town description
     * @param isOpen Whether the town is open for anyone to join
     * @param maxPlayers The maximum number of players allowed
     * @return The created town, or null if a town with that name already exists
     */
    public Town createTown(String name, String description, boolean isOpen, int maxPlayers) {
        return createTown(name, description, isOpen ? Town.JoinType.OPEN : Town.JoinType.CLOSED, maxPlayers);
    }

    /**
     * Gets a town by its ID.
     *
     * @param id The ID of the town
     * @return The town, or null if no town with that ID exists
     */
    public Town getTown(UUID id) {
        return towns.get(id);
    }

    /**
     * Gets a town by its ID.
     * Alias for getTown() to maintain compatibility with network code.
     *
     * @param id The ID of the town
     * @return The town, or null if no town with that ID exists
     */
    public Town getTownById(UUID id) {
        return getTown(id);
    }

    /**
     * Adds a town to the manager.
     *
     * @param town The town to add
     */
    public void addTown(Town town) {
        addTown(town, true);
    }

    /**
     * Adds a town to the manager with optional synchronization.
     *
     * @param town The town to add
     * @param shouldSync Whether to synchronize the town data to clients
     */
    public void addTown(Town town, boolean shouldSync) {
        towns.put(town.getId(), town);

        // Log town addition for debugging
        Pokecobbleclaim.LOGGER.debug("Added town '" + town.getName() + "' to TownManager (total towns: " + towns.size() + ")");

        // Synchronize town data and broadcast to all players only if requested
        if (shouldSync && server != null) {
            TownDataSynchronizer.syncTownData(server, town);
            // Broadcast the updated town list to all players
            TownDataSynchronizer.broadcastTownListUpdate(server);
        }
    }

    /**
     * Saves a town to persistent storage.
     *
     * @param town The town to save
     */
    public void saveTown(Town town) {
        // Add/update the town in our map
        towns.put(town.getId(), town);

        // Save only this specific town to disk
        com.pokecobble.town.data.TownDataStorage.saveTown(town);

        // Synchronize town data and broadcast to all players
        if (server != null) {
            TownDataSynchronizer.syncTownData(server, town);
            // Broadcast the updated town list to all players (in case basic info changed)
            TownDataSynchronizer.broadcastTownListUpdate(server);
        }
    }

    /**
     * Gets a town by its name.
     *
     * @param name The name of the town
     * @return The town, or null if no town with that name exists
     */
    public Town getTownByName(String name) {
        for (Town town : towns.values()) {
            if (town.getName().equalsIgnoreCase(name)) {
                return town;
            }
        }
        return null;
    }

    /**
     * Gets the town a player is in.
     *
     * @param playerId The ID of the player
     * @return The town the player is in, or null if they're not in a town
     */
    public Town getPlayerTown(UUID playerId) {
        UUID townId = playerTowns.get(playerId);
        return townId != null ? towns.get(townId) : null;
    }

    /**
     * Gets the ID of the town a player is in.
     *
     * @param playerId The ID of the player
     * @return The ID of the town the player is in, or null if they're not in a town
     */
    public UUID getPlayerTownId(UUID playerId) {
        return playerTowns.get(playerId);
    }

    /**
     * Gets all towns.
     *
     * @return A collection of all towns
     */
    public Collection<Town> getAllTowns() {
        Pokecobbleclaim.LOGGER.debug("TownManager.getAllTowns() returning " + towns.size() + " towns");
        return towns.values();
    }

    /**
     * Sets the town list.
     * Used by network code to update the client's town list.
     *
     * @param townList The list of towns to set
     */
    public void setTownList(List<Town> townList) {
        towns.clear();
        for (Town town : townList) {
            towns.put(town.getId(), town);
        }
    }

    /**
     * Clears all towns from the manager.
     * Used by network code to reset the client's town list.
     */
    public void clearTowns() {
        towns.clear();
        playerTowns.clear();
    }

    /**
     * Clears all towns but keeps player-town mappings.
     * This is used when loading towns from disk.
     */
    public void clearTownsKeepMappings() {
        towns.clear();
    }

    /**
     * Adds a player to a town.
     *
     * @param playerId The ID of the player
     * @param townId The ID of the town
     * @return true if the player was added, false if they were already in a town or the town doesn't exist
     */
    public boolean addPlayerToTown(UUID playerId, UUID townId) {
        // Check if player is already in a town
        if (playerTowns.containsKey(playerId)) {
            return false;
        }

        Town town = towns.get(townId);
        if (town == null) {
            return false;
        }

        // Check if town is full
        if (town.getPlayerCount() >= town.getMaxPlayers()) {
            return false;
        }

        try {
            // Create TownPlayer object with appropriate rank
            String playerName = getPlayerName(playerId);
            TownPlayerRank rank = town.getPlayerCount() == 0 ? TownPlayerRank.OWNER : TownPlayerRank.MEMBER;
            TownPlayer townPlayer = new TownPlayer(playerId, playerName, rank);

            Pokecobbleclaim.LOGGER.info("Creating TownPlayer: " + playerName + " (" + playerId + ") with rank " + rank + " for town " + town.getName());

            // Add player to town
            town.addPlayer(townPlayer);
            playerTowns.put(playerId, townId);

            // Handle the membership change with centralized data saving
            handlePlayerTownMembershipChange(playerId, null, townId, "joined town " + town.getName());

            // Broadcast player list update to all clients
            if (server != null) {
                broadcastTownPlayerListUpdate(town);
                // Also broadcast town list update (for player count changes)
                TownDataSynchronizer.broadcastTownListUpdate(server);
            }

            Pokecobbleclaim.LOGGER.info("Player " + playerId + " joined town " + town.getName() + " (now " + town.getPlayerCount() + " players)");
            return true;
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error adding player to town: " + e.getMessage());
            return false;
        }
    }

    /**
     * Updates the player-town mapping.
     * This is used when loading towns from disk.
     *
     * @param playerId The ID of the player
     * @param townId The ID of the town
     */
    public void updatePlayerTownMapping(UUID playerId, UUID townId) {
        playerTowns.put(playerId, townId);
    }

    /**
     * Gets debug information about player-town mappings.
     * This is useful for troubleshooting persistence issues.
     */
    public void logPlayerTownMappings() {
        Pokecobbleclaim.LOGGER.info("Current player-town mappings (" + playerTowns.size() + " total):");
        for (Map.Entry<UUID, UUID> entry : playerTowns.entrySet()) {
            UUID playerId = entry.getKey();
            UUID townId = entry.getValue();
            Town town = towns.get(townId);
            String townName = town != null ? town.getName() : "UNKNOWN";
            Pokecobbleclaim.LOGGER.info("  Player " + playerId + " -> Town " + townName + " (" + townId + ")");
        }
    }

    /**
     * Adds a player to a town.
     *
     * @param player The player
     * @param townId The ID of the town
     * @return true if the player was added, false if they were already in a town or the town doesn't exist
     */
    public boolean addPlayerToTown(PlayerEntity player, UUID townId) {
        return addPlayerToTown(player.getUuid(), townId);
    }

    /**
     * Removes a player from their town.
     *
     * @param playerId The ID of the player
     * @return true if the player was removed, false if they weren't in a town
     */
    public boolean removePlayerFromTown(UUID playerId) {
        UUID townId = playerTowns.get(playerId);
        if (townId == null) {
            return false;
        }

        Town town = towns.get(townId);
        if (town == null) {
            playerTowns.remove(playerId);
            return false;
        }

        try {
            String townName = town.getName();

            town.removePlayer(playerId);
            playerTowns.remove(playerId);

            // Handle the membership change with centralized data saving
            handlePlayerTownMembershipChange(playerId, townId, null, "left town " + townName);

            // If town is empty, remove it
            if (town.getPlayerCount() == 0) {
                towns.remove(townId);
                // Delete the town's individual file
                com.pokecobble.town.data.TownDataStorage.deleteTown(townId);
                // Broadcast the updated town list to all players when a town is removed
                if (server != null) {
                    TownDataSynchronizer.broadcastTownListUpdate(server);
                }
                Pokecobbleclaim.LOGGER.info("Removed empty town: " + townName);
            } else {
                // Broadcast player list update to all clients
                if (server != null) {
                    broadcastTownPlayerListUpdate(town);
                    // Also broadcast town list update (for player count changes)
                    TownDataSynchronizer.broadcastTownListUpdate(server);
                }
            }

            Pokecobbleclaim.LOGGER.info("Player " + playerId + " left town " + townName + " (now " + town.getPlayerCount() + " players)");
            return true;
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error removing player from town: " + e.getMessage());
            return false;
        }
    }

    /**
     * Starts an election for a town.
     *
     * @param town The town to start an election for
     * @return The new election, or null if an election is already in progress
     */
    public Election startElection(Town town) {
        if (town == null) {
            return null;
        }

        // Set town to closed during election
        town.setOpen(false);

        // Start the election
        Election election = ElectionManager.getInstance().startElection(town);

        // Notify all players in the town
        if (election != null) {
            notifyTownPlayers(town, "An election has started in " + town.getName() + "!");
        }

        return election;
    }

    /**
     * Notifies all players in a town with a message.
     *
     * @param town The town
     * @param message The message to send
     */
    private void notifyTownPlayers(Town town, String message) {
        if (server == null) {
            // If we don't have a server, just print to console
            System.out.println("Notifying town players: " + message);
            return;
        }

        // Send message to all online players in the town
        for (UUID playerId : town.getPlayers()) {
            ServerPlayerEntity player = server.getPlayerManager().getPlayer(playerId);
            if (player != null) {
                player.sendMessage(Text.literal(message), false);
            }
        }
    }

    /**
     * Updates all towns and elections.
     * Should be called periodically to check for completed elections.
     */
    public void update() {
        // Update elections
        ElectionManager.getInstance().update();

        // Synchronize town data if we have a server
        if (server != null) {
            synchronizeTownData();
        }
    }

    /**
     * Sets the server instance for synchronization.
     * This should be called when the server starts.
     *
     * @param server The server instance
     */
    public void setServer(MinecraftServer server) {
        this.server = server;

        if (server != null) {
            Pokecobbleclaim.LOGGER.info("Server instance set in TownManager");
        }
    }

    /**
     * Synchronizes town data to all relevant players.
     * This method efficiently sends only the changed data to the players who need it.
     */
    public void synchronizeTownData() {
        if (server == null) {
            return;
        }

        // Synchronize each town
        for (Town town : towns.values()) {
            TownDataSynchronizer.syncTownData(server, town);
        }
    }

    /**
     * Resets all player data versions to 0.
     * This should be called when the server starts to ensure version tracking works correctly.
     */
    public void resetAllPlayerDataVersions() {
        for (Town town : towns.values()) {
            // Reset town data version
            town.setDataVersion(0);
            town.clearChangedAspects();

            // Reset player data versions
            for (UUID playerId : town.getPlayers()) {
                TownPlayer player = town.getPlayer(playerId);
                if (player != null) {
                    player.setDataVersion(0);
                }
            }

            // Mark town as having changed data to ensure synchronization after server restart
            // This ensures that when players join, they receive the correct player rank information
            town.markChanged(Town.ASPECT_PLAYERS);
            town.markChanged(Town.ASPECT_RANKS);
            town.markChanged(Town.ASPECT_PERMISSIONS);
        }

        // Reset network synchronizer
        TownDataSynchronizer.resetAllPlayerTownVersions();

        Pokecobbleclaim.LOGGER.info("Reset all town and player data versions");
    }

    /**
     * Handles a player's town membership change by saving all relevant data.
     * This method ensures that both town data and player data are persisted
     * whenever a player joins, leaves, or creates a town.
     *
     * @param playerId The UUID of the player
     * @param oldTownId The ID of the town the player was in (null if none)
     * @param newTownId The ID of the town the player is now in (null if none)
     * @param action The action that caused the change (for logging)
     */
    public void handlePlayerTownMembershipChange(UUID playerId, UUID oldTownId, UUID newTownId, String action) {
        try {
            // Save town data for both old and new towns
            if (oldTownId != null) {
                Town oldTown = towns.get(oldTownId);
                if (oldTown != null) {
                    saveTown(oldTown);
                }
            }

            if (newTownId != null && !newTownId.equals(oldTownId)) {
                Town newTown = towns.get(newTownId);
                if (newTown != null) {
                    saveTown(newTown);
                }
            }

            // Save player data
            TownPlayer townPlayer = null;
            if (newTownId != null) {
                Town newTown = towns.get(newTownId);
                if (newTown != null) {
                    townPlayer = newTown.getPlayer(playerId);
                }
            } else {
                // Player is no longer in a town, create default player data
                // Use MEMBER as default rank for consistency (VISITOR should only be used for actual visitors)
                townPlayer = new TownPlayer(playerId, "Unknown", TownPlayerRank.MEMBER);
            }

            if (townPlayer != null) {
                // Check if player is joining a new town (not just updating existing membership)
                boolean isJoiningNewTown = (oldTownId == null && newTownId != null) ||
                                         (oldTownId != null && newTownId != null && !oldTownId.equals(newTownId));

                if (isJoiningNewTown) {
                    // Set town join time for new town membership
                    setPlayerTownJoinTime(playerId, newTownId);
                }

                com.pokecobble.town.data.PlayerDataStorage.savePlayerData(playerId, townPlayer, newTownId);
            }

            Pokecobbleclaim.LOGGER.info("Handled player town membership change: " + action + " for player " + playerId);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling player town membership change: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Sets the town join time for a player when they join a new town.
     *
     * @param playerId The UUID of the player
     * @param townId The UUID of the town they joined
     */
    private void setPlayerTownJoinTime(UUID playerId, UUID townId) {
        try {
            // Load existing player data
            com.pokecobble.town.data.PlayerDataStorage.SerializablePlayerData playerData =
                com.pokecobble.town.data.PlayerDataStorage.loadPlayerData(playerId);

            if (playerData != null) {
                // Set the town join time to current time
                playerData.setTownJoinTime(System.currentTimeMillis());

                // Get the town player for saving
                Town town = towns.get(townId);
                if (town != null) {
                    TownPlayer townPlayer = town.getPlayer(playerId);
                    if (townPlayer != null) {
                        // Save the updated player data
                        com.pokecobble.town.data.PlayerDataStorage.savePlayerData(playerId, townPlayer, townId);
                        Pokecobbleclaim.LOGGER.debug("Set town join time for player " + playerId + " in town " + townId);
                    }
                }
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error setting town join time for player " + playerId + ": " + e.getMessage());
        }
    }

    /**
     * Saves all town and player data to disk.
     * This method should be called when the server is stopping to ensure
     * all data is persisted.
     */
    public void saveAllData() {
        try {
            // Save all town data
            com.pokecobble.town.data.TownDataStorage.saveTowns();

            // Save all player data for players currently in towns
            for (Map.Entry<UUID, UUID> entry : playerTowns.entrySet()) {
                UUID playerId = entry.getKey();
                UUID townId = entry.getValue();

                Town town = towns.get(townId);
                if (town != null) {
                    TownPlayer townPlayer = town.getPlayer(playerId);
                    if (townPlayer != null) {
                        com.pokecobble.town.data.PlayerDataStorage.savePlayerData(playerId, townPlayer, townId);
                    }
                }
            }

            Pokecobbleclaim.LOGGER.info("Saved all town and player data to disk");

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error saving all data: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Broadcasts a town's player list update to all connected players.
     * This ensures all clients see the updated player count and player list immediately.
     *
     * @param town The town whose player list changed
     */
    public void broadcastTownPlayerListUpdate(Town town) {
        if (server == null || town == null) {
            return;
        }

        try {
            // Get all online players
            List<ServerPlayerEntity> onlinePlayers = server.getPlayerManager().getPlayerList();

            // Send player list update to each player
            for (ServerPlayerEntity player : onlinePlayers) {
                sendTownPlayerListUpdate(player, town);
            }

            Pokecobbleclaim.LOGGER.info("Broadcasted player list update for town " + town.getName() + " to " + onlinePlayers.size() + " players");

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error broadcasting town player list update: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Sends a town's player list update to a specific player.
     *
     * @param player The player to send the update to
     * @param town The town whose player list changed
     */
    private void sendTownPlayerListUpdate(ServerPlayerEntity player, Town town) {
        try {
            // Create packet buffer
            PacketByteBuf buf = com.pokecobble.town.network.NetworkManager.createPacket();

            // Write town UUID
            buf.writeUuid(town.getId());

            // Write town name
            buf.writeString(town.getName());

            // Write player count
            buf.writeInt(town.getPlayerCount());

            // Write max players
            buf.writeInt(town.getMaxPlayers());

            // Write player list
            List<UUID> townPlayers = town.getPlayers();
            buf.writeInt(townPlayers.size());

            for (UUID playerId : townPlayers) {
                buf.writeUuid(playerId);

                // Get player name
                String playerName = getPlayerName(playerId);
                buf.writeString(playerName);

                // Get player rank
                TownPlayer townPlayer = town.getPlayer(playerId);
                String rank = townPlayer != null ? townPlayer.getRank().name() : TownPlayerRank.MEMBER.name();
                buf.writeString(rank);

                // Check if player is online
                boolean isOnline = server.getPlayerManager().getPlayer(playerId) != null;
                buf.writeBoolean(isOnline);
            }

            // Send packet to player
            com.pokecobble.town.network.NetworkManager.sendToPlayer(player, NetworkConstants.TOWN_PLAYER_LIST_UPDATE, buf);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending town player list update to player " + player.getName().getString() + ": " + e.getMessage());
        }
    }
}

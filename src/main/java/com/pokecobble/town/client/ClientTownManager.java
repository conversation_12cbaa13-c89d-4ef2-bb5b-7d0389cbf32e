package com.pokecobble.town.client;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownPlayer;
import com.pokecobble.town.TownPlayerRank;
import com.pokecobble.town.claim.ClaimTag;
import com.pokecobble.town.claim.ClaimHistoryEntry;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Client-side town data manager.
 * Manages cached town data received from the server with proper versioning.
 */
@Environment(EnvType.CLIENT)
public class ClientTownManager {
    private static ClientTownManager instance;
    
    // Cached town data
    private final Map<UUID, Town> towns = new ConcurrentHashMap<>();
    private final Map<UUID, Integer> townVersions = new ConcurrentHashMap<>();
    private final Map<UUID, Long> lastUpdateTimes = new ConcurrentHashMap<>();
    
    // Player's current town cache
    private UUID playerTownId = null;
    private int playerTownVersion = -1;
    
    // Town list cache
    private List<Town> allTowns = new ArrayList<>();
    private int townListVersion = -1;
    private long townListLastUpdate = 0;
    
    // Cache invalidation settings
    private static final long CACHE_TIMEOUT_MS = 300000; // 5 minutes
    private static final long TOWN_LIST_CACHE_TIMEOUT_MS = 600000; // 10 minutes
    
    private ClientTownManager() {}
    
    public static ClientTownManager getInstance() {
        if (instance == null) {
            instance = new ClientTownManager();
        }
        return instance;
    }
    
    /**
     * Updates a town in the cache.
     */
    public void updateTown(Town town, int version) {
        if (town == null) {
            return;
        }
        
        UUID townId = town.getId();
        int currentVersion = townVersions.getOrDefault(townId, -1);
        
        // Only update if version is newer
        if (version > currentVersion) {
            towns.put(townId, town);
            townVersions.put(townId, version);
            lastUpdateTimes.put(townId, System.currentTimeMillis());
            
            // Update town list cache if this town is in it
            updateTownInList(town);
            
            Pokecobbleclaim.LOGGER.debug("Updated town cache for " + town.getName() + " (version " + version + ")");
        }
    }
    
    /**
     * Gets a town from the cache.
     */
    public Town getTown(UUID townId) {
        if (townId == null) {
            return null;
        }
        
        // Check if cache is expired
        Long lastUpdate = lastUpdateTimes.get(townId);
        if (lastUpdate != null && System.currentTimeMillis() - lastUpdate > CACHE_TIMEOUT_MS) {
            // Cache expired, remove from cache
            invalidateTown(townId);
            return null;
        }
        
        return towns.get(townId);
    }
    
    /**
     * Gets the version of a cached town.
     */
    public int getTownVersion(UUID townId) {
        return townVersions.getOrDefault(townId, -1);
    }
    
    /**
     * Invalidates a town from the cache.
     */
    public void invalidateTown(UUID townId) {
        towns.remove(townId);
        townVersions.remove(townId);
        lastUpdateTimes.remove(townId);
        
        // Remove from town list cache
        allTowns.removeIf(town -> town.getId().equals(townId));
    }
    
    /**
     * Sets the player's current town.
     */
    public void setPlayerTown(UUID townId, int version) {
        this.playerTownId = townId;
        this.playerTownVersion = version;
    }
    
    /**
     * Gets the player's current town ID.
     */
    public UUID getPlayerTownId() {
        return playerTownId;
    }
    
    /**
     * Gets the player's current town.
     */
    public Town getPlayerTown() {
        if (playerTownId == null) {
            Pokecobbleclaim.LOGGER.debug("ClientTownManager.getPlayerTown(): playerTownId is null");
            return null;
        }

        Town town = getTown(playerTownId);
        if (town == null) {
            Pokecobbleclaim.LOGGER.warn("ClientTownManager.getPlayerTown(): playerTownId=" + playerTownId + " but town not found in cache");
        } else {
            Pokecobbleclaim.LOGGER.debug("ClientTownManager.getPlayerTown(): found town " + town.getName() + " for player");
        }
        return town;
    }
    
    /**
     * Gets the player's town version.
     */
    public int getPlayerTownVersion() {
        return playerTownVersion;
    }
    
    /**
     * Updates the town list cache.
     */
    public void updateTownList(List<Town> towns, int version) {
        if (version > townListVersion) {
            this.allTowns = new ArrayList<>(towns);
            this.townListVersion = version;
            this.townListLastUpdate = System.currentTimeMillis();

            // Update individual town caches
            for (Town town : towns) {
                updateTown(town, version);
            }

            Pokecobbleclaim.LOGGER.info("Updated town list cache (version " + version + ", " + towns.size() + " towns)");

            // Log town names for debugging
            if (!towns.isEmpty()) {
                StringBuilder townNames = new StringBuilder("Towns in cache: ");
                for (Town town : towns) {
                    townNames.append(town.getName()).append(", ");
                }
                Pokecobbleclaim.LOGGER.info(townNames.toString());
            }
        } else {
            Pokecobbleclaim.LOGGER.debug("Ignored town list update with older version " + version + " (current: " + townListVersion + ")");
        }
    }
    
    /**
     * Gets the cached town list.
     */
    public List<Town> getAllTowns() {
        // Check if cache is expired
        long timeSinceUpdate = System.currentTimeMillis() - townListLastUpdate;
        if (timeSinceUpdate > TOWN_LIST_CACHE_TIMEOUT_MS) {
            // Cache expired, return empty list to trigger refresh
            Pokecobbleclaim.LOGGER.debug("Town list cache expired (age: " + (timeSinceUpdate / 1000) + "s), returning empty list");
            return new ArrayList<>();
        }

        Pokecobbleclaim.LOGGER.debug("Returning cached town list with " + allTowns.size() + " towns (age: " + (timeSinceUpdate / 1000) + "s)");
        return new ArrayList<>(allTowns);
    }
    
    /**
     * Gets the town list version.
     */
    public int getTownListVersion() {
        return townListVersion;
    }
    
    /**
     * Updates a town in the town list cache.
     */
    private void updateTownInList(Town updatedTown) {
        for (int i = 0; i < allTowns.size(); i++) {
            Town town = allTowns.get(i);
            if (town.getId().equals(updatedTown.getId())) {
                allTowns.set(i, updatedTown);
                break;
            }
        }
    }
    
    /**
     * Clears all cached data.
     */
    public void clearCache() {
        towns.clear();
        townVersions.clear();
        lastUpdateTimes.clear();
        allTowns.clear();
        playerTownId = null;
        playerTownVersion = -1;
        townListVersion = -1;
        townListLastUpdate = 0;
        
        Pokecobbleclaim.LOGGER.debug("Cleared town cache");
    }
    
    /**
     * Checks if a town needs to be refreshed from the server.
     */
    public boolean needsRefresh(UUID townId) {
        Long lastUpdate = lastUpdateTimes.get(townId);
        return lastUpdate == null || System.currentTimeMillis() - lastUpdate > CACHE_TIMEOUT_MS;
    }
    
    /**
     * Checks if the town list needs to be refreshed from the server.
     */
    public boolean townListNeedsRefresh() {
        return System.currentTimeMillis() - townListLastUpdate > TOWN_LIST_CACHE_TIMEOUT_MS;
    }
}

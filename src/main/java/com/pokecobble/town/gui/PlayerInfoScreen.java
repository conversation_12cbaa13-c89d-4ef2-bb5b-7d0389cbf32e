package com.pokecobble.town.gui;

import com.pokecobble.town.Town;
import com.pokecobble.town.TownPlayer;
import com.pokecobble.town.TownPlayerRank;
import com.pokecobble.town.data.PlayerDataStorage;
import com.pokecobble.util.MoneyAPI;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.gui.widget.ButtonWidget;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;

/**
 * Screen that displays detailed information about a town player.
 */
public class PlayerInfoScreen extends Screen {
    private final Screen parent;
    private final TownPlayer player;
    private final Town town;

    // Panel dimensions
    private int panelWidth;
    private int panelHeight;

    // UI elements
    private ButtonWidget backButton;

    // Real player data
    private final Date joinDate;
    private final long playerBalance;
    private final Date lastOnline;
    private final int playerLevel;
    private final boolean isPlayerOnline;

    public PlayerInfoScreen(Screen parent, TownPlayer player, Town town) {
        super(Text.literal("Player Info"));
        this.parent = parent;
        this.player = player;
        this.town = town;

        // Get join date and last seen time from player data storage
        PlayerDataStorage.SerializablePlayerData playerData = PlayerDataStorage.loadPlayerData(player.getUuid());
        if (playerData != null) {
            // Use town join time for join date (when they joined the town)
            long townJoinTime = playerData.getTownJoinTime();
            if (townJoinTime > 0) {
                this.joinDate = new Date(townJoinTime);
            } else {
                // Fallback to last login if town join time is not set (for existing players)
                this.joinDate = new Date(playerData.getLastLogin());
            }
        } else {
            // Fallback to a reasonable default if no data exists
            this.joinDate = new Date(System.currentTimeMillis() - (7 * 24 * 60 * 60 * 1000)); // 7 days ago
        }

        // Get player's current balance
        this.playerBalance = getPlayerBalance(player.getUuid());

        // Get last online time
        this.isPlayerOnline = player.isOnline();
        if (isPlayerOnline) {
            this.lastOnline = new Date();
        } else {
            // Use last seen time for offline players
            if (playerData != null) {
                long lastSeenTime = playerData.getLastSeenTime();
                if (lastSeenTime > 0) {
                    this.lastOnline = new Date(lastSeenTime);
                } else {
                    // Fallback to last login if last seen time is not set (for existing players)
                    this.lastOnline = new Date(playerData.getLastLogin());
                }
            } else {
                // Fallback to join date if no player data exists
                this.lastOnline = this.joinDate;
            }
        }

        // Get real-time player data if player is online
        PlayerEntity actualPlayer = getActualPlayer(player.getUuid());

        if (actualPlayer != null) {
            this.playerLevel = actualPlayer.experienceLevel;
        } else {
            // Default values for offline players
            this.playerLevel = 0;
        }
    }

    /**
     * Gets the player's current balance using the MoneyAPI.
     */
    private long getPlayerBalance(UUID playerId) {
        try {
            MinecraftClient client = MinecraftClient.getInstance();
            if (client.player != null && playerId.equals(client.player.getUuid())) {
                // For the current player, we can get balance directly
                return MoneyAPI.getBalance(client.player);
            } else {
                // For other players, we need to check if they're online
                PlayerEntity player = getActualPlayer(playerId);
                if (player != null) {
                    return MoneyAPI.getBalance(player);
                }
            }
        } catch (Exception e) {
            // If there's any error getting balance, return 0
        }
        return 0;
    }

    /**
     * Gets the actual PlayerEntity for a player by UUID.
     */
    private PlayerEntity getActualPlayer(UUID playerId) {
        MinecraftClient client = MinecraftClient.getInstance();
        if (client.world != null) {
            for (PlayerEntity player : client.world.getPlayers()) {
                if (player.getUuid().equals(playerId)) {
                    return player;
                }
            }
        }
        return null;
    }

    @Override
    protected void init() {
        super.init();

        // Calculate panel dimensions based on screen size - more compact
        panelWidth = Math.min(width - 80, 400); // Narrower than main screen
        panelHeight = Math.min(height - 40, 250); // Shorter than main screen

        // Calculate positions
        int leftX = (width - panelWidth) / 2;
        int topY = (height - panelHeight) / 2;

        // Add back button - moved up to ensure it fits
        backButton = ButtonWidget.builder(
                Text.literal("Back"),
                button -> this.close())
                .dimensions(leftX + panelWidth / 2 - 40, topY + panelHeight - 25, 80, 20)
                .build();

        addDrawableChild(backButton);
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Draw dark overlay
        this.renderBackground(context);

        // Calculate positions
        int leftX = (width - panelWidth) / 2;
        int topY = (height - panelHeight) / 2;

        // Draw panel background
        context.fill(leftX, topY, leftX + panelWidth, topY + panelHeight, 0xE0101010);
        context.fillGradient(leftX, topY, leftX + panelWidth, topY + 20, 0xA0303050, 0xA0404060);

        // Draw title
        String title = player.getName() + "'s Information";
        context.drawCenteredTextWithShadow(this.textRenderer, title, width / 2, topY + 6, 0xFFFFFF);

        // Draw content - more compact layout
        int contentX = leftX + 15;
        int contentY = topY + 25;
        int rowHeight = 14; // Reduced from 16
        int sectionSpacing = 6; // Reduced from 10

        // Format dates
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        String joinDateStr = dateFormat.format(joinDate);
        String lastOnlineStr = isPlayerOnline ? "Now (Online)" : dateFormat.format(lastOnline);

        // Player basic info section - more compact header
        context.drawTextWithShadow(this.textRenderer, Text.literal("Basic Information").formatted(Formatting.BOLD, Formatting.YELLOW),
                contentX, contentY, 0xFFFFFF);
        contentY += rowHeight + 2; // Reduced spacing from 5 to 2

        // Use two-column layout for basic info
        TownPlayerRank rank = player.getRank();
        String rankText = rank.getIcon() + " " + rank.getDisplayName();

        // Left column
        int leftColX = contentX;
        int rightColX = contentX + panelWidth/2 - 30;

        // Draw rank with icon and color
        context.drawTextWithShadow(this.textRenderer, Text.literal("Rank: ").formatted(Formatting.GRAY),
                leftColX, contentY, 0xFFFFFF);
        context.drawTextWithShadow(this.textRenderer, rankText,
                leftColX + 45, contentY, rank.getColor());

        // Draw last online in right column
        context.drawTextWithShadow(this.textRenderer, Text.literal("Last seen: ").formatted(Formatting.GRAY),
                rightColX, contentY, 0xFFFFFF);
        int onlineColor = isPlayerOnline ? 0x55FF55 : 0xFFFFFF;
        context.drawTextWithShadow(this.textRenderer, lastOnlineStr,
                rightColX + 60, contentY, onlineColor);
        contentY += rowHeight;

        // Draw join date
        context.drawTextWithShadow(this.textRenderer, Text.literal("Joined: ").formatted(Formatting.GRAY),
                leftColX, contentY, 0xFFFFFF);
        context.drawTextWithShadow(this.textRenderer, joinDateStr,
                leftColX + 45, contentY, 0xFFFFFF);
        contentY += rowHeight;

        // Draw level in second row
        context.drawTextWithShadow(this.textRenderer, Text.literal("Level: ").formatted(Formatting.GRAY),
                leftColX, contentY, 0xFFFFFF);
        context.drawTextWithShadow(this.textRenderer, String.valueOf(playerLevel),
                leftColX + 45, contentY, 0x55FFFF);

        // Draw balance in right column
        context.drawTextWithShadow(this.textRenderer, Text.literal("Balance: ").formatted(Formatting.GRAY),
                rightColX, contentY, 0xFFFFFF);
        context.drawTextWithShadow(this.textRenderer, playerBalance + " coins",
                rightColX + 55, contentY, 0xFFFF55);
        contentY += rowHeight + sectionSpacing;

        // Town Information section - more compact header
        context.drawTextWithShadow(this.textRenderer, Text.literal("Town Information").formatted(Formatting.BOLD, Formatting.YELLOW),
                contentX, contentY, 0xFFFFFF);
        contentY += rowHeight + 2; // Reduced spacing from 5 to 2

        // Show player's town rank
        context.drawTextWithShadow(this.textRenderer, Text.literal("Town Rank: ").formatted(Formatting.GRAY),
                leftColX, contentY, 0xFFFFFF);
        context.drawTextWithShadow(this.textRenderer, player.getRank().getDisplayName(),
                leftColX + 80, contentY, player.getRank().getColor());
        contentY += rowHeight;

        // Show town name if available
        if (town != null) {
            context.drawTextWithShadow(this.textRenderer, Text.literal("Town: ").formatted(Formatting.GRAY),
                    leftColX, contentY, 0xFFFFFF);
            context.drawTextWithShadow(this.textRenderer, town.getName(),
                    leftColX + 40, contentY, 0x55FF55);
        }

        // Draw buttons and other UI elements
        super.render(context, mouseX, mouseY, delta);
    }

    @Override
    public void close() {
        this.client.setScreen(parent);
    }

    @Override
    public boolean shouldPause() {
        return false;
    }
}

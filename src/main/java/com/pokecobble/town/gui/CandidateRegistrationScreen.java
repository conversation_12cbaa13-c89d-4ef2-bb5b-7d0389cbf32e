package com.pokecobble.town.gui;

import com.pokecobble.town.Town;
import com.pokecobble.town.election.Election;
import com.pokecobble.town.election.ElectionManager;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.gui.widget.ButtonWidget;
import net.minecraft.client.gui.widget.TextFieldWidget;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;

/**
 * Screen for registering as a candidate in a town election.
 */
public class CandidateRegistrationScreen extends Screen {
    private final Screen parent;
    private final Town town;
    private TextFieldWidget statementField;
    private ButtonWidget registerButton;
    private ButtonWidget cancelButton;

    // Colors
    private static final int BACKGROUND_COLOR = 0xFF101010; // Solid dark background
    private static final int PANEL_COLOR = 0xFF202030; // Solid panel background
    private static final int BORDER_COLOR = 0xFF5555FF; // Blue border
    private static final int TEXT_COLOR = 0xFFFFFFFF; // White text
    private static final int REGISTER_BUTTON_COLOR = 0xFF4CAF50; // Green register button
    private static final int CANCEL_BUTTON_COLOR = 0xFFE53935; // Red cancel button

    // Maximum statement length
    private static final int MAX_STATEMENT_LENGTH = 120;

    public CandidateRegistrationScreen(Screen parent, Town town) {
        super(Text.literal("Candidate Registration"));
        this.parent = parent;
        this.town = town;
    }

    @Override
    protected void init() {
        super.init();

        int panelWidth = 500;
        int panelHeight = 200;
        int panelX = (this.width - panelWidth) / 2;
        int panelY = (this.height - panelHeight) / 2;
        int buttonWidth = 100;
        int buttonHeight = 20;
        int buttonSpacing = 10;
        int buttonsY = panelY + panelHeight - buttonHeight - 15;

        // Add statement text field
        statementField = new TextFieldWidget(this.textRenderer, panelX + 20, panelY + 70, panelWidth - 40, 60, Text.literal(""));
        statementField.setMaxLength(MAX_STATEMENT_LENGTH);
        statementField.setText("");
        statementField.setEditable(true);
        this.addDrawableChild(statementField);

        // Add register button
        registerButton = ButtonWidget.builder(Text.literal("Register"), button -> {
            try {
                // Register as a candidate
                if (this.client != null && this.client.player != null) {
                    // Get the election from client-side manager
                    Election election = com.pokecobble.town.client.ClientElectionManager.getInstance().getElection(town.getId());
                    if (election != null) {
                        // Register with the statement
                        boolean success = election.registerCandidate(
                            this.client.player.getUuid(),
                            statementField.getText()
                        );

                        // Create a status message
                        String statusMessage = success
                            ? "You have registered as a candidate!"
                            : "Failed to register as a candidate.";
                        Formatting statusColor = success ? Formatting.GREEN : Formatting.RED;

                        // Return to parent screen
                        if (parent instanceof MyTownScreen) {
                            MyTownScreen myTownScreen = (MyTownScreen) parent;
                            myTownScreen.setStatus(statusMessage, statusColor);
                            myTownScreen.refreshPlayerList(); // Refresh the player list
                            myTownScreen.init(); // Refresh the screen
                        }
                    } else {
                        // No election in progress
                        if (parent instanceof MyTownScreen) {
                            ((MyTownScreen) parent).setStatus("No election is in progress.", Formatting.RED);
                        }
                    }
                }

                // Return to parent screen
                this.client.setScreen(parent);
            } catch (Exception e) {
                // Log any errors
                System.err.println("Error registering as candidate: " + e.getMessage());
                e.printStackTrace();

                // Show an error message
                if (parent instanceof MyTownScreen) {
                    ((MyTownScreen) parent).setStatus("Error registering as candidate. Try again.", Formatting.RED);
                }

                // Return to parent screen
                this.client.setScreen(parent);
            }
        })
        .dimensions(panelX + panelWidth / 2 - buttonWidth - buttonSpacing / 2, buttonsY, buttonWidth, buttonHeight)
        .build();
        this.addDrawableChild(registerButton);

        // Add cancel button
        cancelButton = ButtonWidget.builder(Text.literal("Cancel"), button -> {
            // Just close the screen
            this.client.setScreen(parent);
        })
        .dimensions(panelX + panelWidth / 2 + buttonSpacing / 2, buttonsY, buttonWidth, buttonHeight)
        .build();
        this.addDrawableChild(cancelButton);

        // Set initial focus to the statement field
        setInitialFocus(statementField);
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Draw a solid dark background over the entire screen
        context.fill(0, 0, this.width, this.height, BACKGROUND_COLOR);

        // Draw a solid panel
        int panelWidth = 500;
        int panelHeight = 200;
        int panelX = (this.width - panelWidth) / 2;
        int panelY = (this.height - panelHeight) / 2;

        // Draw panel background
        context.fill(panelX, panelY, panelX + panelWidth, panelY + panelHeight, PANEL_COLOR);

        // Draw panel border
        context.drawBorder(panelX, panelY, panelWidth, panelHeight, BORDER_COLOR);

        // Draw title
        context.drawCenteredTextWithShadow(this.textRenderer, Text.literal("Candidate Registration").formatted(Formatting.BOLD),
            panelX + panelWidth / 2, panelY + 15, TEXT_COLOR);

        // Draw instructions
        context.drawTextWithShadow(this.textRenderer, Text.literal("Enter your campaign statement:"),
            panelX + 20, panelY + 50, TEXT_COLOR);

        // Draw character count
        int currentLength = statementField != null ? statementField.getText().length() : 0;
        String countText = currentLength + "/" + MAX_STATEMENT_LENGTH + " characters";
        context.drawTextWithShadow(this.textRenderer, countText,
            panelX + panelWidth - 20 - this.textRenderer.getWidth(countText), panelY + 50, 0xAAAAAA);

        // Draw buttons and text field
        super.render(context, mouseX, mouseY, delta);
    }

    @Override
    public void tick() {
        super.tick();
        if (statementField != null) {
            statementField.tick();
        }
    }

    @Override
    public boolean shouldPause() {
        return false;
    }
}

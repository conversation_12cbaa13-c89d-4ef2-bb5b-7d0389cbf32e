package com.pokecobble.town.gui;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.client.InviteNotification;
import net.minecraft.client.MinecraftClient;
import java.util.UUID;

/**
 * Manages opening and closing the town screen.
 */
public class TownScreenManager {

    /**
     * Opens the town screen using the normal Minecraft GUI.
     */
    public static void openTownScreen() {
        Pokecobbleclaim.LOGGER.info("Opening town screen");
        MinecraftClient client = MinecraftClient.getInstance();
        if (client != null) {
            // Check if there's a pending invitation
            if (InviteNotification.hasPendingInvite()) {
                // Open the invite response screen
                client.execute(() -> client.setScreen(new InviteResponseScreen(
                    client.currentScreen,
                    InviteNotification.getPendingInviteTownId(),
                    InviteNotification.getPendingInviteTownName()
                )));
            } else {
                // Open the normal town screen
                client.execute(() -> client.setScreen(new ModernTownScreen(client.currentScreen)));
            }
        }
    }

    /**
     * Opens the MyTownScreen directly, but only if the player is in a town.
     * This method provides validation before opening the screen.
     */
    public static void openMyTownScreen() {
        MinecraftClient client = MinecraftClient.getInstance();
        if (client != null && client.player != null) {
            // Check if player is in a town
            com.pokecobble.town.Town playerTown = com.pokecobble.town.client.ClientTownManager.getInstance().getPlayerTown();
            UUID playerTownId = com.pokecobble.town.client.ClientTownManager.getInstance().getPlayerTownId();

            // Debug logging to help diagnose the issue
            com.pokecobble.Pokecobbleclaim.LOGGER.info("MyTownScreen access check - PlayerTown: " + (playerTown != null ? playerTown.getName() : "null") +
                                                      ", PlayerTownId: " + playerTownId);

            if (playerTown != null) {
                // Player is in a town, open MyTownScreen
                client.execute(() -> client.setScreen(new com.pokecobble.town.gui.MyTownScreen(client.currentScreen)));
            } else if (playerTownId != null) {
                // Player has a town ID but town data is not cached - request fresh data and try again
                com.pokecobble.Pokecobbleclaim.LOGGER.info("Player has town ID " + playerTownId + " but town data not cached, requesting fresh data");
                com.pokecobble.town.network.town.TownNetworkHandler.requestTownData();

                // Show a loading message and try again after a short delay
                com.pokecobble.town.client.NotificationRenderer.addNotification("Loading town data...");

                // Schedule multiple retries with shorter delays to be more responsive
                client.execute(() -> {
                    new Thread(() -> {
                        try {
                            // Try multiple times with shorter delays for better responsiveness
                            for (int attempt = 1; attempt <= 5; attempt++) {
                                Thread.sleep(200 * attempt); // Progressive delay: 200ms, 400ms, 600ms, 800ms, 1000ms

                                final int currentAttempt = attempt;
                                client.execute(() -> {
                                    com.pokecobble.town.Town retryPlayerTown = com.pokecobble.town.client.ClientTownManager.getInstance().getPlayerTown();
                                    if (retryPlayerTown != null) {
                                        com.pokecobble.Pokecobbleclaim.LOGGER.info("Successfully loaded town data on attempt " + currentAttempt + ", opening MyTownScreen");
                                        client.setScreen(new com.pokecobble.town.gui.MyTownScreen(client.currentScreen));
                                        return; // Exit the retry loop
                                    } else if (currentAttempt == 5) {
                                        // Final attempt failed
                                        com.pokecobble.town.client.NotificationRenderer.addNotification("Failed to load town data. Please try again.");
                                        com.pokecobble.Pokecobbleclaim.LOGGER.warn("Failed to load town data after 5 attempts");
                                    }
                                });

                                // Check if we should break early (town data loaded)
                                if (com.pokecobble.town.client.ClientTownManager.getInstance().getPlayerTown() != null) {
                                    break;
                                }
                            }
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                        }
                    }).start();
                });
            } else {
                // Player is not in a town, show notification
                com.pokecobble.town.client.NotificationRenderer.addNotification("You are not a member of any town");
            }
        }
    }
}

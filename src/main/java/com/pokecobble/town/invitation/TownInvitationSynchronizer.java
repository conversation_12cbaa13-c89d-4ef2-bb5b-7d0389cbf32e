package com.pokecobble.town.invitation;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.network.NetworkConstants;
import com.pokecobble.town.network.NetworkManager;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking;
import net.fabricmc.fabric.api.networking.v1.PacketByteBufs;
import net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.util.Identifier;

import java.util.List;
import java.util.UUID;

/**
 * Handles synchronization of town invitations between server and client.
 */
public class TownInvitationSynchronizer {
    
    // Network identifiers
    public static final Identifier INVITATION_SYNC = new Identifier("pokecobbleclaim", "invitation_sync");
    public static final Identifier INVITATION_ACCEPT = new Identifier("pokecobbleclaim", "invitation_accept");
    public static final Identifier INVITATION_DECLINE = new Identifier("pokecobbleclaim", "invitation_decline");
    public static final Identifier INVITATION_REQUEST = new Identifier("pokecobbleclaim", "invitation_request");
    
    /**
     * Registers server-side packet handlers.
     */
    public static void registerServerHandlers() {
        // Register invitation accept handler
        ServerPlayNetworking.registerGlobalReceiver(INVITATION_ACCEPT, TownInvitationSynchronizer::handleInvitationAccept);
        
        // Register invitation decline handler
        ServerPlayNetworking.registerGlobalReceiver(INVITATION_DECLINE, TownInvitationSynchronizer::handleInvitationDecline);
        
        // Register invitation request handler
        ServerPlayNetworking.registerGlobalReceiver(INVITATION_REQUEST, TownInvitationSynchronizer::handleInvitationRequest);
    }
    
    /**
     * Registers client-side packet handlers.
     */
    @Environment(EnvType.CLIENT)
    public static void registerClientHandlers() {
        // Register invitation sync handler
        ClientPlayNetworking.registerGlobalReceiver(INVITATION_SYNC, TownInvitationSynchronizer::handleInvitationSync);
    }
    
    /**
     * Synchronizes player invitations to the client.
     */
    public static void syncPlayerInvitations(MinecraftServer server, UUID playerId) {
        ServerPlayerEntity player = server.getPlayerManager().getPlayer(playerId);
        if (player == null) {
            return;
        }
        
        try {
            TownInvitationManager manager = TownInvitationManager.getInstance();
            List<TownInvitation> invitations = manager.getPlayerInvitations(playerId);
            
            PacketByteBuf buf = PacketByteBufs.create();
            
            // Write data version
            int version = manager.getPlayerInvitationVersion(playerId);
            buf.writeInt(version);
            
            // Write number of invitations
            buf.writeInt(invitations.size());
            
            // Write each invitation
            for (TownInvitation invitation : invitations) {
                // Skip expired invitations
                if (invitation.isExpired()) {
                    continue;
                }
                
                buf.writeUuid(invitation.getId());
                buf.writeUuid(invitation.getFromTownId());
                buf.writeUuid(invitation.getToPlayerId());
                buf.writeUuid(invitation.getInvitedByPlayerId());
                buf.writeLong(invitation.getCreatedTime());
                buf.writeLong(invitation.getExpiryTime());
                buf.writeInt(invitation.getStatus().ordinal());
                
                // Write town name for display
                Town town = TownManager.getInstance().getTownById(invitation.getFromTownId());
                String townName = town != null ? town.getName() : "Unknown Town";
                buf.writeString(townName, NetworkConstants.MAX_STRING_LENGTH);
            }
            
            // Send packet (bypass rate limiting for automatic invitation sync)
            NetworkManager.sendToPlayer(player, INVITATION_SYNC, buf);
            
            Pokecobbleclaim.LOGGER.debug("Synchronized " + invitations.size() + " invitations to player " + player.getName().getString());
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error synchronizing invitations to player " + playerId + ": " + e.getMessage());
        }
    }
    
    /**
     * Handles invitation accept from client.
     */
    private static void handleInvitationAccept(MinecraftServer server, ServerPlayerEntity player,
                                             net.minecraft.server.network.ServerPlayNetworkHandler handler,
                                             PacketByteBuf buf,
                                             net.fabricmc.fabric.api.networking.v1.PacketSender sender) {
        try {
            UUID invitationId = buf.readUuid();
            UUID playerId = player.getUuid();
            
            TownInvitationManager manager = TownInvitationManager.getInstance();
            boolean accepted = manager.acceptInvitation(server, invitationId, playerId);
            
            if (accepted) {
                Pokecobbleclaim.LOGGER.info("Player " + player.getName().getString() + " accepted town invitation " + invitationId);
            } else {
                Pokecobbleclaim.LOGGER.warn("Failed to accept invitation " + invitationId + " for player " + player.getName().getString());
            }
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling invitation accept: " + e.getMessage());
        }
    }
    
    /**
     * Handles invitation decline from client.
     */
    private static void handleInvitationDecline(MinecraftServer server, ServerPlayerEntity player,
                                              net.minecraft.server.network.ServerPlayNetworkHandler handler,
                                              PacketByteBuf buf,
                                              net.fabricmc.fabric.api.networking.v1.PacketSender sender) {
        try {
            UUID invitationId = buf.readUuid();
            UUID playerId = player.getUuid();
            
            TownInvitationManager manager = TownInvitationManager.getInstance();
            boolean declined = manager.declineInvitation(server, invitationId, playerId);
            
            if (declined) {
                Pokecobbleclaim.LOGGER.info("Player " + player.getName().getString() + " declined town invitation " + invitationId);
            } else {
                Pokecobbleclaim.LOGGER.warn("Failed to decline invitation " + invitationId + " for player " + player.getName().getString());
            }
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling invitation decline: " + e.getMessage());
        }
    }
    
    /**
     * Handles invitation request from client.
     */
    private static void handleInvitationRequest(MinecraftServer server, ServerPlayerEntity player,
                                              net.minecraft.server.network.ServerPlayNetworkHandler handler,
                                              PacketByteBuf buf,
                                              net.fabricmc.fabric.api.networking.v1.PacketSender sender) {
        try {
            UUID playerId = player.getUuid();
            
            // Sync current invitations
            syncPlayerInvitations(server, playerId);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling invitation request: " + e.getMessage());
        }
    }
    
    /**
     * Handles invitation sync from server.
     */
    @Environment(EnvType.CLIENT)
    private static void handleInvitationSync(net.minecraft.client.MinecraftClient client,
                                           net.minecraft.client.network.ClientPlayNetworkHandler handler,
                                           PacketByteBuf buf,
                                           net.fabricmc.fabric.api.networking.v1.PacketSender sender) {
        try {
            // Read data version
            int version = buf.readInt();
            
            // Read number of invitations
            int invitationCount = buf.readInt();
            
            // Read each invitation
            for (int i = 0; i < invitationCount; i++) {
                UUID id = buf.readUuid();
                UUID fromTownId = buf.readUuid();
                UUID toPlayerId = buf.readUuid();
                UUID invitedByPlayerId = buf.readUuid();
                long createdTime = buf.readLong();
                long expiryTime = buf.readLong();
                TownInvitation.InvitationStatus status = TownInvitation.InvitationStatus.values()[buf.readInt()];
                String townName = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                
                // Create invitation object
                TownInvitation invitation = new TownInvitation(id, fromTownId, toPlayerId, invitedByPlayerId, createdTime, expiryTime);
                invitation.setStatus(status);
                
                // Update client-side invitation manager
                com.pokecobble.town.client.ClientInvitationManager.getInstance().updateInvitation(invitation, townName, version);
            }
            
            // Fire sync event
            com.pokecobble.town.client.ClientSyncManager.getInstance().fireEvent("invitations_updated", invitationCount);
            
            Pokecobbleclaim.LOGGER.debug("Received invitation sync: " + invitationCount + " invitations (version " + version + ")");
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling invitation sync: " + e.getMessage());
        }
    }
    
    /**
     * Sends invitation accept to server.
     */
    @Environment(EnvType.CLIENT)
    public static void sendInvitationAccept(UUID invitationId) {
        try {
            PacketByteBuf buf = PacketByteBufs.create();
            buf.writeUuid(invitationId);
            
            NetworkManager.sendToServer(INVITATION_ACCEPT, buf);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending invitation accept: " + e.getMessage());
        }
    }
    
    /**
     * Sends invitation decline to server.
     */
    @Environment(EnvType.CLIENT)
    public static void sendInvitationDecline(UUID invitationId) {
        try {
            PacketByteBuf buf = PacketByteBufs.create();
            buf.writeUuid(invitationId);
            
            NetworkManager.sendToServer(INVITATION_DECLINE, buf);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending invitation decline: " + e.getMessage());
        }
    }
    
    /**
     * Requests invitation sync from server.
     */
    @Environment(EnvType.CLIENT)
    public static void requestInvitationSync() {
        try {
            PacketByteBuf buf = PacketByteBufs.create();
            
            NetworkManager.sendToServer(INVITATION_REQUEST, buf);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error requesting invitation sync: " + e.getMessage());
        }
    }
}

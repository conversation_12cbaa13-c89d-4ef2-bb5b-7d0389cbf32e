package com.pokecobble.town.config;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.config.ConfigSynchronizer;

import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.minecraft.client.MinecraftClient;

import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.lang.reflect.Type;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Manages town-specific settings and synchronizes them between client and server.
 * Enhanced with persistent storage to ensure settings survive server restarts.
 */
public class TownSettingsManager {
    private static TownSettingsManager instance;

    // Town settings storage (server-side) - using ConcurrentHashMap for thread safety
    private static final Map<UUID, Map<String, Object>> townSettings = new ConcurrentHashMap<>();

    // Gson instance for JSON serialization
    private static final Gson GSON = new GsonBuilder().setPrettyPrinting().create();

    // File extension for town settings files
    private static final String SETTINGS_FILE_EXTENSION = ".settings.json";

    // Directory name for town settings
    private static final String SETTINGS_DIR_NAME = "town_settings";

    // Type token for Gson deserialization
    private static final Type SETTINGS_TYPE = new TypeToken<Map<String, Object>>(){}.getType();
    
    private TownSettingsManager() {
    }

    public static TownSettingsManager getInstance() {
        if (instance == null) {
            instance = new TownSettingsManager();
        }
        return instance;
    }

    /**
     * Gets the town settings directory.
     *
     * @return The town settings directory
     */
    private static File getTownSettingsDirectory() {
        File configDir = new File("config");
        File townSettingsDir = new File(configDir, SETTINGS_DIR_NAME);
        if (!townSettingsDir.exists()) {
            townSettingsDir.mkdirs();
        }
        return townSettingsDir;
    }

    /**
     * Gets the settings file for a specific town.
     *
     * @param townId The town ID
     * @return The settings file
     */
    private static File getTownSettingsFile(UUID townId) {
        File settingsDir = getTownSettingsDirectory();
        return new File(settingsDir, townId.toString() + SETTINGS_FILE_EXTENSION);
    }

    /**
     * Saves town settings to disk immediately.
     *
     * @param townId The town ID
     * @param settings The settings to save
     */
    private static void saveTownSettingsToDisk(UUID townId, Map<String, Object> settings) {
        if (settings == null || settings.isEmpty()) {
            // Delete the settings file if no custom settings exist
            File settingsFile = getTownSettingsFile(townId);
            if (settingsFile.exists()) {
                if (settingsFile.delete()) {
                    Pokecobbleclaim.LOGGER.debug("Deleted empty settings file for town " + townId);
                } else {
                    Pokecobbleclaim.LOGGER.warn("Failed to delete empty settings file for town " + townId);
                }
            }
            return;
        }

        // Validate settings before saving
        if (!validateTownSettings(settings)) {
            Pokecobbleclaim.LOGGER.warn("Attempting to save invalid settings for town " + townId + ", sanitizing first");
            settings = sanitizeTownSettings(settings);
            if (settings.isEmpty()) {
                Pokecobbleclaim.LOGGER.error("Cannot save town settings for " + townId + " - no valid settings after sanitization");
                return;
            }
        }

        try {
            File settingsFile = getTownSettingsFile(townId);
            File tempFile = File.createTempFile("town_settings_", ".tmp", settingsFile.getParentFile());

            // Write to temporary file first
            try (FileWriter writer = new FileWriter(tempFile)) {
                GSON.toJson(settings, writer);
            }

            // Atomic move to final location
            if (tempFile.renameTo(settingsFile)) {
                Pokecobbleclaim.LOGGER.info("Saved settings to disk for town " + townId);
            } else {
                throw new IOException("Failed to move temporary file to final location");
            }

        } catch (IOException e) {
            Pokecobbleclaim.LOGGER.error("Failed to save settings for town " + townId + ": " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Loads town settings from disk.
     *
     * @param townId The town ID
     * @return The loaded settings, or null if no settings file exists
     */
    private static Map<String, Object> loadTownSettingsFromDisk(UUID townId) {
        File settingsFile = getTownSettingsFile(townId);
        if (!settingsFile.exists()) {
            return null;
        }

        try (FileReader reader = new FileReader(settingsFile)) {
            Map<String, Object> settings = GSON.fromJson(reader, SETTINGS_TYPE);
            if (settings != null) {
                // Validate and sanitize loaded settings
                if (validateTownSettings(settings)) {
                    Pokecobbleclaim.LOGGER.debug("Loaded valid settings from disk for town " + townId + ": " + settings);
                    return settings;
                } else {
                    // Try to sanitize invalid settings
                    Map<String, Object> sanitized = sanitizeTownSettings(settings);
                    if (!sanitized.isEmpty()) {
                        Pokecobbleclaim.LOGGER.warn("Loaded and sanitized invalid settings for town " + townId + ": " + sanitized);
                        return sanitized;
                    } else {
                        Pokecobbleclaim.LOGGER.error("Settings file for town " + townId + " contains no valid settings");
                    }
                }
            }
        } catch (IOException e) {
            Pokecobbleclaim.LOGGER.error("Failed to load settings for town " + townId + ": " + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to parse settings file for town " + townId + ": " + e.getMessage());
            e.printStackTrace();
        }

        return null;
    }
    
    /**
     * Gets town settings for a specific town.
     * Enhanced to load from persistent storage if not in memory.
     *
     * @param townId The ID of the town
     * @return Map of town settings
     */
    public static Map<String, Object> getTownSettings(UUID townId) {
        Map<String, Object> settings = townSettings.get(townId);
        if (settings == null) {
            // Try to load settings from disk first
            settings = loadTownSettingsFromDisk(townId);
            if (settings != null) {
                // Store loaded settings in memory
                townSettings.put(townId, new ConcurrentHashMap<>(settings));
                Pokecobbleclaim.LOGGER.debug("Loaded town settings from disk for town " + townId);
            } else {
                // No settings on disk, create defaults
                settings = createDefaultTownSettings(townId);
                townSettings.put(townId, settings);
                Pokecobbleclaim.LOGGER.debug("Created default settings for town " + townId);
            }
        }
        return new HashMap<>(settings);
    }

    /**
     * Gets existing town settings without creating defaults.
     * Used for saving to disk to avoid saving default values.
     *
     * @param townId The ID of the town
     * @return Map of existing town settings, or null if none exist
     */
    public static Map<String, Object> getExistingTownSettings(UUID townId) {
        Map<String, Object> settings = townSettings.get(townId);
        return settings != null ? new HashMap<>(settings) : null;
    }

    /**
     * Sets a town setting with immediate persistence to disk.
     *
     * @param townId The ID of the town
     * @param key The setting key
     * @param value The setting value
     */
    public static void setTownSetting(UUID townId, String key, Object value) {
        // Get or create settings map
        Map<String, Object> settings = townSettings.computeIfAbsent(townId, k -> {
            // Try to load existing settings first
            Map<String, Object> loadedSettings = loadTownSettingsFromDisk(townId);
            if (loadedSettings != null) {
                return new ConcurrentHashMap<>(loadedSettings);
            } else {
                return createDefaultTownSettings(townId);
            }
        });

        // Update the setting
        settings.put(key, value);

        // Apply the setting to the town object
        applySettingToTown(townId, key, value);

        // IMMEDIATE PERSISTENCE: Save settings directly to disk
        saveTownSettingsToDisk(townId, settings);

        // Also save the town to disk to maintain consistency
        com.pokecobble.town.Town town = com.pokecobble.town.TownManager.getInstance().getTownById(townId);
        if (town != null) {
            com.pokecobble.town.data.TownDataStorage.saveTown(town);
            Pokecobbleclaim.LOGGER.info("Saved town " + town.getName() + " and settings to disk after setting " + key);

            // Sync updated settings to all online players
            try {
                net.minecraft.server.MinecraftServer server = com.pokecobble.Pokecobbleclaim.getServer();
                if (server != null) {
                    com.pokecobble.config.ConfigSynchronizer.syncAllTownSettingsToAllPlayers(server);
                    Pokecobbleclaim.LOGGER.debug("Synced town setting change to all online players");
                } else {
                    Pokecobbleclaim.LOGGER.debug("Server not available, skipping town settings sync");
                }
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.warn("Could not sync town settings to players after change: " + e.getMessage());
            }
        }

        Pokecobbleclaim.LOGGER.info("Set town setting " + key + " = " + value + " for town " + townId);
    }
    
    /**
     * Sets multiple town settings with immediate persistence to disk.
     *
     * @param townId The ID of the town
     * @param settings Map of settings to set
     */
    public static void setTownSettings(UUID townId, Map<String, Object> settings) {
        // Get or create settings map
        Map<String, Object> townSettingsMap = townSettings.computeIfAbsent(townId, k -> {
            // Try to load existing settings first
            Map<String, Object> loadedSettings = loadTownSettingsFromDisk(townId);
            if (loadedSettings != null) {
                return new ConcurrentHashMap<>(loadedSettings);
            } else {
                return createDefaultTownSettings(townId);
            }
        });

        // Update all settings
        townSettingsMap.putAll(settings);

        // Apply settings to the town object in the correct order
        // First apply the image setting if present
        if (settings.containsKey("image")) {
            applySettingToTown(townId, "image", settings.get("image"));
        }

        // Then apply other settings
        for (Map.Entry<String, Object> entry : settings.entrySet()) {
            if (!"image".equals(entry.getKey())) { // Skip image as it was already applied
                applySettingToTown(townId, entry.getKey(), entry.getValue());
            }
        }

        // IMMEDIATE PERSISTENCE: Save settings directly to disk
        saveTownSettingsToDisk(townId, townSettingsMap);

        // Also save the town to disk to maintain consistency
        com.pokecobble.town.Town town = com.pokecobble.town.TownManager.getInstance().getTownById(townId);
        if (town != null) {
            com.pokecobble.town.data.TownDataStorage.saveTown(town);
            Pokecobbleclaim.LOGGER.info("Saved town " + town.getName() + " and settings to disk after settings update");

            // Sync updated settings to all online players
            try {
                net.minecraft.server.MinecraftServer server = com.pokecobble.Pokecobbleclaim.getServer();
                if (server != null) {
                    com.pokecobble.config.ConfigSynchronizer.syncAllTownSettingsToAllPlayers(server);
                    Pokecobbleclaim.LOGGER.debug("Synced town settings change to all online players");
                } else {
                    Pokecobbleclaim.LOGGER.debug("Server not available, skipping town settings sync");
                }
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.warn("Could not sync town settings to players after change: " + e.getMessage());
            }
        }

        Pokecobbleclaim.LOGGER.info("Set " + settings.size() + " town settings for town " + townId);
    }

    /**
     * Loads all town settings from disk during server startup.
     * This ensures all settings are available in memory when players join.
     */
    public static void loadAllTownSettingsFromDisk() {
        File settingsDir = getTownSettingsDirectory();
        if (!settingsDir.exists()) {
            Pokecobbleclaim.LOGGER.info("No town settings directory found, starting with empty settings");
            return;
        }

        File[] settingsFiles = settingsDir.listFiles((dir, name) -> name.endsWith(SETTINGS_FILE_EXTENSION));
        if (settingsFiles == null || settingsFiles.length == 0) {
            Pokecobbleclaim.LOGGER.info("No town settings files found");
            return;
        }

        int loadedCount = 0;
        int errorCount = 0;

        for (File settingsFile : settingsFiles) {
            try {
                // Extract town ID from filename
                String filename = settingsFile.getName();
                String townIdStr = filename.substring(0, filename.length() - SETTINGS_FILE_EXTENSION.length());
                UUID townId = UUID.fromString(townIdStr);

                // Load settings from file
                Map<String, Object> settings = loadTownSettingsFromDisk(townId);
                if (settings != null) {
                    townSettings.put(townId, new ConcurrentHashMap<>(settings));
                    loadedCount++;
                    Pokecobbleclaim.LOGGER.debug("Loaded settings for town " + townId + ": " + settings);
                }

            } catch (Exception e) {
                errorCount++;
                Pokecobbleclaim.LOGGER.error("Failed to load settings from file " + settingsFile.getName() + ": " + e.getMessage());
            }
        }

        Pokecobbleclaim.LOGGER.info("Loaded town settings from disk: " + loadedCount + " successful, " + errorCount + " errors");
    }

    /**
     * Validates town settings to ensure they contain valid values.
     *
     * @param settings The settings to validate
     * @return true if settings are valid, false otherwise
     */
    private static boolean validateTownSettings(Map<String, Object> settings) {
        if (settings == null) {
            return false;
        }

        try {
            // Validate each known setting
            for (Map.Entry<String, Object> entry : settings.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();

                switch (key) {
                    case "isOpen":
                        if (!(value instanceof Boolean)) {
                            Pokecobbleclaim.LOGGER.warn("Invalid boolean value for setting " + key + ": " + value);
                            return false;
                        }
                        break;
                    case "name":
                    case "description":
                        if (!(value instanceof String)) {
                            Pokecobbleclaim.LOGGER.warn("Invalid string value for setting " + key + ": " + value);
                            return false;
                        }
                        break;
                    case "maxPlayers":
                        if (!(value instanceof Number)) {
                            Pokecobbleclaim.LOGGER.warn("Invalid number value for setting " + key + ": " + value);
                            return false;
                        }
                        break;
                    default:
                        // Allow unknown settings for future compatibility
                        Pokecobbleclaim.LOGGER.debug("Unknown setting: " + key + " = " + value);
                        break;
                }
            }
            return true;
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error validating town settings: " + e.getMessage());
            return false;
        }
    }

    /**
     * Sanitizes town settings by removing invalid entries and fixing types.
     *
     * @param settings The settings to sanitize
     * @return Sanitized settings map
     */
    private static Map<String, Object> sanitizeTownSettings(Map<String, Object> settings) {
        if (settings == null) {
            return new HashMap<>();
        }

        Map<String, Object> sanitized = new HashMap<>();

        for (Map.Entry<String, Object> entry : settings.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            try {
                switch (key) {
                    case "isOpen":
                        // Convert to boolean if possible
                        if (value instanceof Boolean) {
                            sanitized.put(key, value);
                        } else if (value instanceof String) {
                            sanitized.put(key, Boolean.parseBoolean((String) value));
                        } else {
                            Pokecobbleclaim.LOGGER.warn("Skipping invalid boolean setting " + key + ": " + value);
                        }
                        break;
                    case "name":
                    case "description":
                        // Convert to string if possible
                        if (value instanceof String) {
                            String stringValue = ((String) value).trim();
                            if (!stringValue.isEmpty()) {
                                sanitized.put(key, stringValue);
                            } else {
                                Pokecobbleclaim.LOGGER.warn("Skipping empty string setting " + key);
                            }
                        } else {
                            sanitized.put(key, String.valueOf(value));
                        }
                        break;
                    case "maxPlayers":
                        // Convert to integer if possible
                        if (value instanceof Number) {
                            int intValue = ((Number) value).intValue();
                            if (intValue >= 1 && intValue <= 100) {
                                sanitized.put(key, intValue);
                            } else {
                                Pokecobbleclaim.LOGGER.warn("Skipping invalid maxPlayers value " + intValue + " (must be 1-100)");
                            }
                        } else if (value instanceof String) {
                            try {
                                int intValue = Integer.parseInt((String) value);
                                if (intValue >= 1 && intValue <= 100) {
                                    sanitized.put(key, intValue);
                                } else {
                                    Pokecobbleclaim.LOGGER.warn("Skipping invalid maxPlayers value " + intValue + " (must be 1-100)");
                                }
                            } catch (NumberFormatException e) {
                                Pokecobbleclaim.LOGGER.warn("Skipping invalid maxPlayers string: " + value);
                            }
                        } else {
                            Pokecobbleclaim.LOGGER.warn("Skipping invalid maxPlayers setting " + key + ": " + value);
                        }
                        break;
                    default:
                        // Keep unknown settings as-is for future compatibility
                        sanitized.put(key, value);
                        break;
                }
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.warn("Error sanitizing setting " + key + ": " + e.getMessage());
            }
        }

        return sanitized;
    }

    /**
     * Applies all persisted settings to a town object.
     * This should be called when a town is loaded from disk to ensure
     * the town object reflects the persisted settings.
     *
     * @param town The town to apply settings to
     */
    public static void applyPersistedSettingsToTown(Town town) {
        if (town == null) {
            return;
        }

        try {
            Map<String, Object> settings = getTownSettings(town.getId());
            if (settings == null || settings.isEmpty()) {
                Pokecobbleclaim.LOGGER.debug("No persisted settings found for town " + town.getName() + ", using defaults");
                return;
            }

            // Apply each setting to the town object
            for (Map.Entry<String, Object> entry : settings.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();

                try {
                    applySettingToTown(town.getId(), key, value);
                } catch (Exception e) {
                    Pokecobbleclaim.LOGGER.warn("Failed to apply persisted setting " + key + " = " + value + " to town " + town.getName() + ": " + e.getMessage());
                }
            }

            Pokecobbleclaim.LOGGER.debug("Applied " + settings.size() + " persisted settings to town " + town.getName());

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error applying persisted settings to town " + town.getName() + ": " + e.getMessage());
        }
    }

    /**
     * Applies all persisted settings to all loaded towns.
     * This should be called during server startup after towns are loaded.
     */
    public static void applyPersistedSettingsToAllTowns() {
        try {
            Collection<Town> allTowns = TownManager.getInstance().getAllTowns();
            int appliedCount = 0;

            for (Town town : allTowns) {
                applyPersistedSettingsToTown(town);
                appliedCount++;
            }

            Pokecobbleclaim.LOGGER.info("Applied persisted settings to " + appliedCount + " towns");

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error applying persisted settings to all towns: " + e.getMessage());
        }
    }

    /**
     * Handles corrupted settings files by creating backups and attempting recovery.
     *
     * @param townId The town ID
     * @param settingsFile The corrupted settings file
     */
    private static void handleCorruptedSettingsFile(UUID townId, File settingsFile) {
        try {
            // Create backup of corrupted file
            File backupFile = new File(settingsFile.getParent(),
                settingsFile.getName() + ".corrupted." + System.currentTimeMillis());

            if (settingsFile.renameTo(backupFile)) {
                Pokecobbleclaim.LOGGER.warn("Moved corrupted settings file for town " + townId + " to: " + backupFile.getName());

                // Try to recover from town data
                com.pokecobble.town.Town town = com.pokecobble.town.TownManager.getInstance().getTownById(townId);
                if (town != null) {
                    // Create new default settings based on current town state
                    Map<String, Object> recoveredSettings = createDefaultTownSettings(townId);
                    saveTownSettingsToDisk(townId, recoveredSettings);
                    Pokecobbleclaim.LOGGER.info("Recovered settings for town " + townId + " using default values");
                }
            } else {
                Pokecobbleclaim.LOGGER.error("Failed to backup corrupted settings file for town " + townId);
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling corrupted settings file for town " + townId + ": " + e.getMessage());
        }
    }

    /**
     * Performs a health check on all town settings files and repairs any issues.
     */
    public static void performSettingsHealthCheck() {
        Pokecobbleclaim.LOGGER.info("Performing town settings health check...");

        File settingsDir = getTownSettingsDirectory();
        if (!settingsDir.exists()) {
            Pokecobbleclaim.LOGGER.info("No settings directory found, health check complete");
            return;
        }

        File[] settingsFiles = settingsDir.listFiles((dir, name) -> name.endsWith(SETTINGS_FILE_EXTENSION));
        if (settingsFiles == null || settingsFiles.length == 0) {
            Pokecobbleclaim.LOGGER.info("No settings files found, health check complete");
            return;
        }

        int checkedCount = 0;
        int repairedCount = 0;
        int errorCount = 0;

        for (File settingsFile : settingsFiles) {
            try {
                // Extract town ID from filename
                String filename = settingsFile.getName();
                String townIdStr = filename.substring(0, filename.length() - SETTINGS_FILE_EXTENSION.length());
                UUID townId = UUID.fromString(townIdStr);

                // Try to load and validate settings
                Map<String, Object> settings = loadTownSettingsFromDisk(townId);
                if (settings == null) {
                    // File exists but couldn't be loaded - likely corrupted
                    handleCorruptedSettingsFile(townId, settingsFile);
                    repairedCount++;
                } else if (!validateTownSettings(settings)) {
                    // Settings loaded but invalid - sanitize and save
                    Map<String, Object> sanitized = sanitizeTownSettings(settings);
                    if (!sanitized.isEmpty()) {
                        saveTownSettingsToDisk(townId, sanitized);
                        repairedCount++;
                        Pokecobbleclaim.LOGGER.info("Repaired invalid settings for town " + townId);
                    } else {
                        handleCorruptedSettingsFile(townId, settingsFile);
                        repairedCount++;
                    }
                }

                checkedCount++;

            } catch (Exception e) {
                errorCount++;
                Pokecobbleclaim.LOGGER.error("Error during health check of file " + settingsFile.getName() + ": " + e.getMessage());
            }
        }

        Pokecobbleclaim.LOGGER.info("Town settings health check complete: " + checkedCount + " checked, " + repairedCount + " repaired, " + errorCount + " errors");
    }

    /**
     * Creates default town settings based on the town's current state.
     *
     * @param townId The ID of the town
     * @return Map of default settings
     */
    private static Map<String, Object> createDefaultTownSettings(UUID townId) {
        Map<String, Object> settings = new HashMap<>();

        // Get the town to read current values
        Town town = TownManager.getInstance().getTown(townId);
        if (town != null) {
            // Basic town properties
            settings.put("name", town.getName());
            settings.put("description", town.getDescription());
            settings.put("maxPlayers", town.getMaxPlayers());
            settings.put("isOpen", town.getJoinType() == Town.JoinType.OPEN);

            // Image settings
            settings.put("image", town.getImage() != null ? town.getImage() : "default");

            // Try to load existing image settings from TownImageUtil
            String imageName = town.getImage();
            if (imageName != null && !imageName.isEmpty()) {
                com.pokecobble.town.util.TownImageUtil.ImageSettings imageSettings =
                    com.pokecobble.town.util.TownImageUtil.getImageSettings(town, imageName);
                if (imageSettings != null) {
                    Map<String, Object> imageSettingsMap = new HashMap<>();
                    imageSettingsMap.put("scale", imageSettings.scale);
                    imageSettingsMap.put("offsetX", imageSettings.offsetX);
                    imageSettingsMap.put("offsetY", imageSettings.offsetY);
                    settings.put("imageSettings", imageSettingsMap);
                } else {
                    // Default image settings
                    Map<String, Object> imageSettingsMap = new HashMap<>();
                    imageSettingsMap.put("scale", 1.0f);
                    imageSettingsMap.put("offsetX", 0);
                    imageSettingsMap.put("offsetY", 0);
                    settings.put("imageSettings", imageSettingsMap);
                }
            } else {
                // Default image settings for default image
                Map<String, Object> imageSettingsMap = new HashMap<>();
                imageSettingsMap.put("scale", 1.0f);
                imageSettingsMap.put("offsetX", 0);
                imageSettingsMap.put("offsetY", 0);
                settings.put("imageSettings", imageSettingsMap);
            }

        } else {
            // Default values if town not found
            settings.put("name", "Unknown Town");
            settings.put("description", "A lovely town");
            settings.put("maxPlayers", 20);
            settings.put("isOpen", true);
            settings.put("image", "default");

            // Default image settings
            Map<String, Object> imageSettingsMap = new HashMap<>();
            imageSettingsMap.put("scale", 1.0f);
            imageSettingsMap.put("offsetX", 0);
            imageSettingsMap.put("offsetY", 0);
            settings.put("imageSettings", imageSettingsMap);
        }

        return settings;
    }
    
    /**
     * Applies a setting to the actual town object.
     *
     * @param townId The ID of the town
     * @param key The setting key
     * @param value The setting value
     */
    private static void applySettingToTown(UUID townId, String key, Object value) {
        // Try to get town from TownManager first
        Town town = TownManager.getInstance().getTown(townId);

        // On client side, also try ClientTownManager
        if (town == null && net.fabricmc.api.EnvType.CLIENT.equals(net.fabricmc.loader.api.FabricLoader.getInstance().getEnvironmentType())) {
            try {
                town = com.pokecobble.town.client.ClientTownManager.getInstance().getTown(townId);
            } catch (Exception e) {
                // ClientTownManager might not be available on server side
            }
        }

        if (town == null) {
            Pokecobbleclaim.LOGGER.warn("Cannot apply setting to town " + townId + " - town not found in TownManager or ClientTownManager");
            return;
        }

        try {
            switch (key) {
                case "isOpen":
                    boolean isOpen = (Boolean) value;
                    town.setJoinType(isOpen ? Town.JoinType.OPEN : Town.JoinType.CLOSED);

                    // On client side, also update the town in ClientTownManager if it exists there
                    if (net.fabricmc.api.EnvType.CLIENT.equals(net.fabricmc.loader.api.FabricLoader.getInstance().getEnvironmentType())) {
                        try {
                            Town clientTown = com.pokecobble.town.client.ClientTownManager.getInstance().getTown(townId);
                            if (clientTown != null && clientTown != town) {
                                clientTown.setJoinType(isOpen ? Town.JoinType.OPEN : Town.JoinType.CLOSED);
                                // Force update the ClientTownManager cache with the updated town
                                com.pokecobble.town.client.ClientTownManager.getInstance().updateTown(clientTown, clientTown.getDataVersion() + 1);
                                Pokecobbleclaim.LOGGER.debug("Updated isOpen setting in ClientTownManager for town " + townId);
                            }
                        } catch (Exception e) {
                            // ClientTownManager might not be available
                        }
                    }
                    break;
                case "name":
                    String newName = (String) value;
                    town.setName(newName);

                    // On client side, also update the town in ClientTownManager if it exists there
                    if (net.fabricmc.api.EnvType.CLIENT.equals(net.fabricmc.loader.api.FabricLoader.getInstance().getEnvironmentType())) {
                        try {
                            Town clientTown = com.pokecobble.town.client.ClientTownManager.getInstance().getTown(townId);
                            if (clientTown != null && clientTown != town) {
                                clientTown.setName(newName);
                                com.pokecobble.town.client.ClientTownManager.getInstance().updateTown(clientTown, clientTown.getDataVersion() + 1);
                                Pokecobbleclaim.LOGGER.debug("Updated name setting in ClientTownManager for town " + townId);
                            }
                        } catch (Exception e) {
                            // ClientTownManager might not be available
                        }
                    }
                    break;
                case "description":
                    String newDescription = (String) value;
                    town.setDescription(newDescription);

                    // On client side, also update the town in ClientTownManager if it exists there
                    if (net.fabricmc.api.EnvType.CLIENT.equals(net.fabricmc.loader.api.FabricLoader.getInstance().getEnvironmentType())) {
                        try {
                            Town clientTown = com.pokecobble.town.client.ClientTownManager.getInstance().getTown(townId);
                            if (clientTown != null && clientTown != town) {
                                clientTown.setDescription(newDescription);
                                com.pokecobble.town.client.ClientTownManager.getInstance().updateTown(clientTown, clientTown.getDataVersion() + 1);
                                Pokecobbleclaim.LOGGER.debug("Updated description setting in ClientTownManager for town " + townId);
                            }
                        } catch (Exception e) {
                            // ClientTownManager might not be available
                        }
                    }
                    break;
                case "maxPlayers":
                    int newMaxPlayers = ((Number) value).intValue();
                    town.setMaxPlayers(newMaxPlayers);

                    // On client side, also update the town in ClientTownManager if it exists there
                    if (net.fabricmc.api.EnvType.CLIENT.equals(net.fabricmc.loader.api.FabricLoader.getInstance().getEnvironmentType())) {
                        try {
                            Town clientTown = com.pokecobble.town.client.ClientTownManager.getInstance().getTown(townId);
                            if (clientTown != null && clientTown != town) {
                                clientTown.setMaxPlayers(newMaxPlayers);
                                com.pokecobble.town.client.ClientTownManager.getInstance().updateTown(clientTown, clientTown.getDataVersion() + 1);
                                Pokecobbleclaim.LOGGER.debug("Updated maxPlayers setting in ClientTownManager for town " + townId);
                            }
                        } catch (Exception e) {
                            // ClientTownManager might not be available
                        }
                    }
                    break;
                case "image":
                    String newImage = (String) value;
                    town.setImage(newImage);

                    // On client side, also update the town in ClientTownManager if it exists there
                    if (net.fabricmc.api.EnvType.CLIENT.equals(net.fabricmc.loader.api.FabricLoader.getInstance().getEnvironmentType())) {
                        try {
                            Town clientTown = com.pokecobble.town.client.ClientTownManager.getInstance().getTown(townId);
                            if (clientTown != null && clientTown != town) {
                                clientTown.setImage(newImage);
                                com.pokecobble.town.client.ClientTownManager.getInstance().updateTown(clientTown, clientTown.getDataVersion() + 1);
                                Pokecobbleclaim.LOGGER.debug("Updated image setting in ClientTownManager for town " + townId);
                            }
                        } catch (Exception e) {
                            // ClientTownManager might not be available
                        }
                    }
                    break;
                case "imageSettings":
                    if (value instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> imageSettingsMap = (Map<String, Object>) value;

                        // Extract image settings
                        float scale = 1.0f;
                        int offsetX = 0;
                        int offsetY = 0;

                        if (imageSettingsMap.containsKey("scale")) {
                            scale = ((Number) imageSettingsMap.get("scale")).floatValue();
                        }
                        if (imageSettingsMap.containsKey("offsetX")) {
                            offsetX = ((Number) imageSettingsMap.get("offsetX")).intValue();
                        }
                        if (imageSettingsMap.containsKey("offsetY")) {
                            offsetY = ((Number) imageSettingsMap.get("offsetY")).intValue();
                        }

                        // Apply image settings using TownImageUtil (client-side only)
                        String imageName = town.getImage();
                        if (imageName != null && !imageName.isEmpty()) {
                            // Only apply locally on client side
                            if (net.fabricmc.loader.api.FabricLoader.getInstance().getEnvironmentType() == net.fabricmc.api.EnvType.CLIENT) {
                                com.pokecobble.town.util.TownImageUtil.applyImageSettingsLocally(town, imageName, scale, offsetX, offsetY);
                            }
                            Pokecobbleclaim.LOGGER.debug("Applied image settings for town " + townId + ": scale=" + scale + ", offsetX=" + offsetX + ", offsetY=" + offsetY);
                        }
                    }
                    break;

                default:
                    Pokecobbleclaim.LOGGER.warn("Unknown town setting: " + key);
                    break;
            }

            // Save the town after applying settings (only on server side)
            if (!net.fabricmc.api.EnvType.CLIENT.equals(net.fabricmc.loader.api.FabricLoader.getInstance().getEnvironmentType())) {
                TownManager.getInstance().saveTown(town);
            } else {
                Pokecobbleclaim.LOGGER.debug("Applied town setting " + key + " = " + value + " to client-side town " + townId);
            }

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error applying town setting " + key + ": " + e.getMessage());
        }
    }
    
    /**
     * Gets the current player's town settings (client-side).
     *
     * @return Map of town settings, or empty map if player is not in a town
     */
    @Environment(EnvType.CLIENT)
    public static Map<String, Object> getCurrentPlayerTownSettings() {
        MinecraftClient client = MinecraftClient.getInstance();
        if (client.player == null) {
            return new HashMap<>();
        }
        
        Town playerTown = com.pokecobble.town.client.ClientTownManager.getInstance().getPlayerTown();

        if (playerTown == null) {
            return new HashMap<>();
        }
        
        return getTownSettings(playerTown.getId());
    }
    
    /**
     * Updates the current player's town settings (client-side).
     *
     * @param key The setting key
     * @param value The setting value
     */
    @Environment(EnvType.CLIENT)
    public static void updateCurrentPlayerTownSetting(String key, Object value) {
        MinecraftClient client = MinecraftClient.getInstance();
        if (client.player == null) {
            return;
        }

        Town playerTown = com.pokecobble.town.client.ClientTownManager.getInstance().getPlayerTown();

        if (playerTown == null) {
            Pokecobbleclaim.LOGGER.warn("Player is not in a town, cannot update town settings");
            return;
        }

        // Update local settings
        setTownSetting(playerTown.getId(), key, value);

        // Create a map with the single setting to send to server
        Map<String, Object> settingsUpdate = new HashMap<>();
        settingsUpdate.put(key, value);

        // Send the update to the server via the config synchronizer
        Pokecobbleclaim.LOGGER.info("CLIENT: Sending town setting update to server - " + key + " = " + value);
        ConfigSynchronizer.sendConfigUpdate(ConfigSynchronizer.CATEGORY_TOWN, settingsUpdate);

        // NOTE: We do NOT update user preferences because town settings are GLOBAL for the entire town,
        // not per-player. The server will sync the updated settings back to ALL players in the town.

        Pokecobbleclaim.LOGGER.info("CLIENT: Updated town setting " + key + " = " + value + " for player's town and sent to server");
    }
    
    // NOTE: Removed syncTownSettingsToPreferences method because town settings should be GLOBAL for the entire town,
    // not stored in per-player user preferences. Town settings are managed through TownSettingsManager only.
    
    /**
     * Clears town settings for a specific town from both memory and disk.
     *
     * @param townId The ID of the town
     */
    public static void clearTownSettings(UUID townId) {
        // Remove from memory
        townSettings.remove(townId);

        // Remove from disk
        File settingsFile = getTownSettingsFile(townId);
        if (settingsFile.exists()) {
            if (settingsFile.delete()) {
                Pokecobbleclaim.LOGGER.debug("Deleted settings file for town " + townId);
            } else {
                Pokecobbleclaim.LOGGER.warn("Failed to delete settings file for town " + townId);
            }
        }

        Pokecobbleclaim.LOGGER.debug("Cleared town settings for town " + townId);
    }

    /**
     * Comprehensive test method for town settings persistence across server restarts.
     * Tests immediate persistence, validation, error handling, and recovery.
     */
    public static void testTownSettingsPersistence() {
        Pokecobbleclaim.LOGGER.info("=== Testing Town Settings Persistence (Enhanced) ===");

        try {
            // Test 1: Basic persistence test
            testBasicPersistence();

            // Test 2: Validation and sanitization test
            testValidationAndSanitization();

            // Test 3: Error handling and recovery test
            testErrorHandlingAndRecovery();

            // Test 4: Health check test
            testHealthCheck();

            Pokecobbleclaim.LOGGER.info("=== All Town Settings Persistence Tests Completed Successfully ===");

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Town settings persistence test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Tests basic settings persistence functionality.
     */
    private static void testBasicPersistence() {
        Pokecobbleclaim.LOGGER.info("--- Testing Basic Persistence ---");

        try {
            // Create a test town
            com.pokecobble.town.Town testTown = new com.pokecobble.town.Town("TestBasicPersistence");
            UUID townId = testTown.getId();
            com.pokecobble.town.TownManager.getInstance().addTown(testTown, false);

            Pokecobbleclaim.LOGGER.info("Created test town: " + testTown.getName() + " with ID: " + townId);

            // Set some custom settings
            setTownSetting(townId, "name", "TestTownRenamed");
            setTownSetting(townId, "description", "A test town for persistence");
            setTownSetting(townId, "maxPlayers", 15);
            setTownSetting(townId, "isOpen", false);

            Map<String, Object> originalSettings = getTownSettings(townId);
            Pokecobbleclaim.LOGGER.info("Original settings: " + originalSettings);

            // Save to disk
            com.pokecobble.town.data.TownDataStorage.saveTown(testTown);
            Pokecobbleclaim.LOGGER.info("Saved town to disk");

            // Clear from memory (simulate server restart)
            clearTownSettings(townId);
            // Note: We can't easily remove from TownManager for testing, so we'll just clear settings

            // Load from disk
            com.pokecobble.town.Town loadedTown = com.pokecobble.town.data.TownDataStorage.loadTown(townId);
            if (loadedTown != null) {
                com.pokecobble.town.TownManager.getInstance().addTown(loadedTown, false);

                Map<String, Object> loadedSettings = getTownSettings(townId);
                Pokecobbleclaim.LOGGER.info("Loaded settings: " + loadedSettings);

                // Compare
                boolean matches = originalSettings.equals(loadedSettings);
                if (matches) {
                    Pokecobbleclaim.LOGGER.info("SUCCESS: Basic persistence test passed");
                } else {
                    Pokecobbleclaim.LOGGER.error("FAILURE: Basic persistence test failed");
                    Pokecobbleclaim.LOGGER.error("Original: " + originalSettings);
                    Pokecobbleclaim.LOGGER.error("Loaded: " + loadedSettings);

                    // Check individual settings
                    for (String key : originalSettings.keySet()) {
                        Object originalValue = originalSettings.get(key);
                        Object loadedValue = loadedSettings.get(key);
                        if (!originalValue.equals(loadedValue)) {
                            Pokecobbleclaim.LOGGER.error("Setting mismatch - " + key + ": original=" + originalValue + ", loaded=" + loadedValue);
                        }
                    }
                }

                // Clean up
                clearTownSettings(townId);
            } else {
                Pokecobbleclaim.LOGGER.error("Failed to load town from disk!");
            }

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Basic persistence test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Tests validation and sanitization functionality.
     */
    private static void testValidationAndSanitization() {
        Pokecobbleclaim.LOGGER.info("--- Testing Validation and Sanitization ---");

        try {
            // Test valid settings
            Map<String, Object> validSettings = new HashMap<>();
            validSettings.put("name", "TestTown");
            validSettings.put("description", "A test town");
            validSettings.put("maxPlayers", 25);
            validSettings.put("isOpen", true);

            boolean isValid = validateTownSettings(validSettings);
            if (isValid) {
                Pokecobbleclaim.LOGGER.info("SUCCESS: Valid settings passed validation");
            } else {
                Pokecobbleclaim.LOGGER.error("FAILURE: Valid settings failed validation");
            }

            // Test invalid settings
            Map<String, Object> invalidSettings = new HashMap<>();
            invalidSettings.put("name", 123); // Should be string
            invalidSettings.put("description", true); // Should be string
            invalidSettings.put("maxPlayers", "not_a_number"); // Should be number
            invalidSettings.put("isOpen", "not_a_boolean"); // Should be boolean

            boolean isInvalid = !validateTownSettings(invalidSettings);
            if (isInvalid) {
                Pokecobbleclaim.LOGGER.info("SUCCESS: Invalid settings correctly failed validation");
            } else {
                Pokecobbleclaim.LOGGER.error("FAILURE: Invalid settings incorrectly passed validation");
            }

            // Test sanitization
            Map<String, Object> mixedSettings = new HashMap<>();
            mixedSettings.put("name", "TestTown");  // Valid string
            mixedSettings.put("description", "A test town");  // Valid string
            mixedSettings.put("maxPlayers", "25");  // String that can be converted to number
            mixedSettings.put("isOpen", "true");  // String that can be converted to boolean
            mixedSettings.put("unknownSetting", "value");  // Unknown setting

            Map<String, Object> sanitized = sanitizeTownSettings(mixedSettings);

            if (sanitized.containsKey("name") && sanitized.get("name").equals("TestTown") &&
                sanitized.containsKey("description") && sanitized.get("description").equals("A test town") &&
                sanitized.containsKey("maxPlayers") && sanitized.get("maxPlayers").equals(25) &&
                sanitized.containsKey("isOpen") && sanitized.get("isOpen").equals(true) &&
                sanitized.containsKey("unknownSetting")) {  // Should be kept for future compatibility
                Pokecobbleclaim.LOGGER.info("SUCCESS: Sanitization worked correctly");
            } else {
                Pokecobbleclaim.LOGGER.error("FAILURE: Sanitization did not work as expected");
                Pokecobbleclaim.LOGGER.error("Sanitized: " + sanitized);
            }

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Validation and sanitization test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Tests error handling and recovery functionality.
     */
    private static void testErrorHandlingAndRecovery() {
        Pokecobbleclaim.LOGGER.info("--- Testing Error Handling and Recovery ---");

        try {
            // Create a test town
            com.pokecobble.town.Town testTown = new com.pokecobble.town.Town("TestErrorHandling");
            UUID townId = testTown.getId();
            com.pokecobble.town.TownManager.getInstance().addTown(testTown, false);

            // Create a corrupted settings file
            File settingsFile = getTownSettingsFile(townId);
            settingsFile.getParentFile().mkdirs();

            try (FileWriter writer = new FileWriter(settingsFile)) {
                writer.write("{ invalid json content }");
            }

            Pokecobbleclaim.LOGGER.info("Created corrupted settings file");

            // Try to load settings - should handle corruption gracefully
            Map<String, Object> settings = loadTownSettingsFromDisk(townId);
            if (settings == null) {
                Pokecobbleclaim.LOGGER.info("SUCCESS: Corrupted file handled gracefully (returned null)");
            } else {
                Pokecobbleclaim.LOGGER.error("FAILURE: Corrupted file should have returned null");
            }

            // Test recovery by creating valid settings
            setTownSetting(townId, "isOpen", true);
            Map<String, Object> recoveredSettings = getTownSettings(townId);

            if (recoveredSettings != null && !recoveredSettings.isEmpty()) {
                Pokecobbleclaim.LOGGER.info("SUCCESS: Recovery after corruption worked");
            } else {
                Pokecobbleclaim.LOGGER.error("FAILURE: Recovery after corruption failed");
            }

            // Clean up
            clearTownSettings(townId);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling and recovery test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Tests health check functionality.
     */
    private static void testHealthCheck() {
        Pokecobbleclaim.LOGGER.info("--- Testing Health Check ---");

        try {
            // Create some test towns with various states
            com.pokecobble.town.Town testTown1 = new com.pokecobble.town.Town("TestHealthCheck1");
            com.pokecobble.town.Town testTown2 = new com.pokecobble.town.Town("TestHealthCheck2");

            UUID townId1 = testTown1.getId();
            UUID townId2 = testTown2.getId();

            com.pokecobble.town.TownManager.getInstance().addTown(testTown1, false);
            com.pokecobble.town.TownManager.getInstance().addTown(testTown2, false);

            // Create valid settings for town1
            setTownSetting(townId1, "isOpen", true);

            // Create invalid settings file for town2
            File settingsFile2 = getTownSettingsFile(townId2);
            settingsFile2.getParentFile().mkdirs();
            try (FileWriter writer = new FileWriter(settingsFile2)) {
                writer.write("{ \"isOpen\": \"invalid_boolean_value\" }");
            }

            Pokecobbleclaim.LOGGER.info("Created test scenarios for health check");

            // Run health check
            performSettingsHealthCheck();

            // Verify results
            Map<String, Object> settings1 = getTownSettings(townId1);
            Map<String, Object> settings2 = getTownSettings(townId2);

            if (settings1 != null && settings2 != null) {
                Pokecobbleclaim.LOGGER.info("SUCCESS: Health check completed and all towns have settings");
            } else {
                Pokecobbleclaim.LOGGER.error("FAILURE: Health check did not ensure all towns have settings");
            }

            // Clean up
            clearTownSettings(townId1);
            clearTownSettings(townId2);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Health check test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
}

plugins {
	id 'fabric-loom' version '1.10-SNAPSHOT'
	id 'maven-publish'
}

version = project.mod_version
group = project.maven_group

base {
	archivesName = project.archives_base_name
}

repositories {
	// Add repositories to retrieve artifacts from in here.
	// You should only use this when depending on other mods because
	// <PERSON><PERSON> adds the essential maven repositories to download Minecraft and libraries from automatically.
	// See https://docs.gradle.org/current/userguide/declaring_repositories.html
	// for more information about repositories.

	// Maven Central for additional dependencies
	mavenCentral()

	// JitPack for GitHub-based dependencies
	maven { url = 'https://jitpack.io' }


}

fabricApi {
	configureDataGeneration {
		client = true
	}
}

dependencies {
	// To change the versions see the gradle.properties file
	minecraft "com.mojang:minecraft:${project.minecraft_version}"
	mappings "net.fabricmc:yarn:${project.yarn_mappings}:v2"
	modImplementation "net.fabricmc:fabric-loader:${project.loader_version}"

	// Fabric API. This is technically optional, but you probably want it anyway.
	modImplementation "net.fabricmc.fabric-api:fabric-api:${project.fabric_version}"



}

processResources {
	inputs.property "version", project.version

	filesMatching("fabric.mod.json") {
		expand "version": inputs.properties.version
	}

	// Add a property to identify development environment
	filesMatching("fabric.mod.json") {
		expand "dev_environment": "true"
	}
}

tasks.withType(JavaCompile).configureEach {
	it.options.release = 17
	it.options.compilerArgs.addAll(['-Xlint:unchecked', '-Xlint:deprecation'])
}

java {
	// Loom will automatically attach sourcesJar to a RemapSourcesJar task and to the "build" task
	// if it is present.
	// If you remove this line, sources will not be generated.
	withSourcesJar()

	sourceCompatibility = JavaVersion.VERSION_17
	targetCompatibility = JavaVersion.VERSION_17
}

jar {
	inputs.property "archivesName", project.base.archivesName

	from("LICENSE") {
		rename { "${it}_${inputs.properties.archivesName}"}
	}

	// Handle duplicate entries
	duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}

// Development environment run configurations
loom {
	runs {
		client {
			client()
			setConfigName("Minecraft Client")
			ideConfigGenerated(true)
			runDir("run")
			// Add JVM arguments to ensure fresh mod loading
			vmArgs("-Dfabric.development=true", "-Dfabric.reload.debugger=true")
		}
		server {
			server()
			setConfigName("Minecraft Server")
			ideConfigGenerated(true)
			runDir("run")
			// Add JVM arguments to ensure fresh mod loading
			vmArgs("-Dfabric.development=true")
		}
	}
}

// Add a task to clean the run directory to ensure fresh mod loading
task cleanRun(type: Delete) {
	group = "fabric"
	description = "Clean the run directory to ensure fresh mod loading"
	delete "${project.projectDir}/run/mods"
}

// Make runClient depend on cleanRun to ensure fresh mod loading
tasks.named('runClient').configure {
	dependsOn(cleanRun)
	// Always rebuild before running
	dependsOn('build')
}

// Make runServer depend on cleanRun to ensure fresh mod loading
tasks.named('runServer').configure {
	dependsOn(cleanRun)
	// Always rebuild before running
	dependsOn('build')
}

// Add a custom task to force rebuild and run client
task forceRebuildAndRunClient {
	group = "fabric"
	description = "Force rebuild and run client"
	dependsOn('clean', 'runClient')
}

// Add a custom task to force rebuild and run server
task forceRebuildAndRunServer {
	group = "fabric"
	description = "Force rebuild and run server"
	dependsOn('clean', 'runServer')
}

// configure the maven publication
publishing {
	publications {
		create("mavenJava", MavenPublication) {
			artifactId = project.archives_base_name
			from components.java
		}
	}

	// See https://docs.gradle.org/current/userguide/publishing_maven.html for information on how to set up publishing.
	repositories {
		// Add repositories to publish to here.
		// Notice: This block does NOT have the same function as the block in the top level.
		// The repositories here will be used for publishing your artifact, not for
		// retrieving dependencies.
	}
}
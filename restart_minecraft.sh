#!/bin/bash

# Script to restart Minecraft client and server for PokeCobbleClaim mod development and view logs
# Usage: ./restart_minecraft.sh [action] [options...]
#
# Restart actions: client, server, both, interactive
# Log actions: logs, client-log, server-log, runtime-log, debug-log
# View modes: tail, less, cat, editor

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to kill processes by name
kill_gradle_process() {
    local process_name=$1
    print_status "Looking for $process_name processes..."
    
    # Find and kill gradle processes containing the process name
    local pids=$(ps aux | grep -i "gradle.*$process_name" | grep -v grep | awk '{print $2}')
    
    if [ -n "$pids" ]; then
        print_status "Found $process_name processes with PIDs: $pids"
        echo $pids | xargs kill -TERM 2>/dev/null
        sleep 2
        
        # Force kill if still running
        local remaining_pids=$(ps aux | grep -i "gradle.*$process_name" | grep -v grep | awk '{print $2}')
        if [ -n "$remaining_pids" ]; then
            print_warning "Force killing remaining $process_name processes..."
            echo $remaining_pids | xargs kill -KILL 2>/dev/null
        fi
        
        print_success "Stopped $process_name"
    else
        print_warning "No $process_name processes found"
    fi
}

# Function to start a gradle task in background
start_gradle_task() {
    local task_name=$1
    print_status "Starting $task_name..."
    
    # Start the gradle task in background
    nohup ./gradlew $task_name > ${task_name}.log 2>&1 &
    local pid=$!
    
    print_success "Started $task_name with PID: $pid"
    print_status "Logs are being written to ${task_name}.log"
}

# Function to wait for process to start
wait_for_startup() {
    local task_name=$1
    local log_file="${task_name}.log"
    local timeout=60
    local count=0

    print_status "Waiting for $task_name to start (timeout: ${timeout}s)..."

    while [ $count -lt $timeout ]; do
        if [ -f "$log_file" ]; then
            # Check for common startup indicators
            if grep -q "BUILD SUCCESSFUL\|Server thread\|Client thread\|Started\|Ready" "$log_file" 2>/dev/null; then
                print_success "$task_name appears to have started successfully"
                return 0
            fi

            # Check for errors
            if grep -q "BUILD FAILED\|FAILURE\|Exception\|Error" "$log_file" 2>/dev/null; then
                print_error "$task_name failed to start. Check ${log_file} for details."
                return 1
            fi
        fi

        sleep 1
        count=$((count + 1))

        # Show progress every 10 seconds
        if [ $((count % 10)) -eq 0 ]; then
            print_status "Still waiting for $task_name... (${count}s elapsed)"
        fi
    done

    print_warning "$task_name startup timeout reached. Check ${log_file} for status."
    return 1
}

# Function to open logs
open_logs() {
    local log_type=${1:-menu}
    local view_mode=${2:-tail}

    case $log_type in
        "client-gradle"|"cg")
            local log_file="runClient.log"
            ;;
        "server-gradle"|"sg")
            local log_file="runServer.log"
            ;;
        "server-runtime"|"sr")
            local log_file="run/logs/latest.log"
            ;;
        "server-debug"|"sd")
            local log_file="run/logs/debug.log"
            ;;
        "menu"|"")
            print_status "Available log files:"
            echo "  1. Client Gradle Log (runClient.log)"
            echo "  2. Server Gradle Log (runServer.log)"
            echo "  3. Server Runtime Log (run/logs/latest.log)"
            echo "  4. Server Debug Log (run/logs/debug.log)"
            echo
            echo -n "Select log to view (1-4): "
            read -n 1 choice
            echo

            case $choice in
                "1") open_logs "client-gradle" "$view_mode" ;;
                "2") open_logs "server-gradle" "$view_mode" ;;
                "3") open_logs "server-runtime" "$view_mode" ;;
                "4") open_logs "server-debug" "$view_mode" ;;
                *) print_error "Invalid choice: $choice" ; return 1 ;;
            esac
            return
            ;;
        *)
            print_error "Unknown log type: $log_type"
            echo "Available types: client-gradle (cg), server-gradle (sg), server-runtime (sr), server-debug (sd)"
            return 1
            ;;
    esac

    if [ ! -f "$log_file" ]; then
        print_error "Log file not found: $log_file"
        return 1
    fi

    print_status "Opening $log_file with $view_mode mode..."

    case $view_mode in
        "tail"|"t")
            print_status "Following log file (Ctrl+C to exit)..."
            tail -f "$log_file"
            ;;
        "less"|"l")
            less "$log_file"
            ;;
        "cat"|"c")
            cat "$log_file"
            ;;
        "editor"|"e")
            ${EDITOR:-nano} "$log_file"
            ;;
        *)
            print_error "Unknown view mode: $view_mode"
            echo "Available modes: tail (t), less (l), cat (c), editor (e)"
            return 1
            ;;
    esac
}

# Function to restart both client and server
restart_both() {
    print_status "Restarting both Minecraft Client and Server for PokeCobbleClaim mod..."

    # Stop both
    kill_gradle_process "runclient"
    kill_gradle_process "runserver"

    sleep 2

    # Start both simultaneously
    print_status "Starting server and client simultaneously..."
    start_gradle_task "runServer"
    start_gradle_task "runClient"

    # Wait for both to start (in parallel)
    print_status "Waiting for both processes to start..."

    # Start background processes to monitor each startup
    (wait_for_startup "runServer" && print_success "Server startup completed") &
    server_wait_pid=$!

    (wait_for_startup "runClient" && print_success "Client startup completed") &
    client_wait_pid=$!

    # Wait for both monitoring processes to complete
    wait $server_wait_pid
    server_result=$?

    wait $client_wait_pid
    client_result=$?

    echo
    if [ $server_result -eq 0 ] && [ $client_result -eq 0 ]; then
        print_success "Both server and client started successfully!"
    elif [ $server_result -eq 0 ]; then
        print_warning "Server started successfully, but client had issues. Check runClient.log"
    elif [ $client_result -eq 0 ]; then
        print_warning "Client started successfully, but server had issues. Check runServer.log"
    else
        print_error "Both server and client had startup issues. Check the log files."
    fi

    print_status "You can monitor the logs with:"
    echo "  tail -f runClient.log"
    echo "  tail -f runServer.log"
    echo
}

# Interactive mode function
interactive_mode() {
    print_status "PokeCobbleClaim Interactive Mode - Available commands:"
    echo "  r - Restart both client and server"
    echo "  c - Restart client only"
    echo "  s - Restart server only"
    echo "  l - View logs (interactive menu)"
    echo "  1 - View client gradle log"
    echo "  2 - View server gradle log"
    echo "  3 - View server runtime log"
    echo "  4 - View server debug log"
    echo "  q - Quit"
    echo

    while true; do
        echo -n "Command: "
        read -n 1 input
        echo  # New line after input

        case $input in
            "r"|"R")
                restart_both
                ;;
            "c"|"C")
                print_status "Restarting Minecraft Client..."
                kill_gradle_process "runclient"
                sleep 1
                start_gradle_task "runClient"
                wait_for_startup "runClient"
                ;;
            "s"|"S")
                print_status "Restarting Minecraft Server..."
                kill_gradle_process "runserver"
                sleep 1
                start_gradle_task "runServer"
                wait_for_startup "runServer"
                ;;
            "l"|"L")
                open_logs
                ;;
            "1")
                open_logs "client-gradle"
                ;;
            "2")
                open_logs "server-gradle"
                ;;
            "3")
                open_logs "server-runtime"
                ;;
            "4")
                open_logs "server-debug"
                ;;
            "q"|"Q")
                print_status "Exiting interactive mode..."
                exit 0
                ;;
            "")
                # Handle enter key
                continue
                ;;
            *)
                print_warning "Unknown command: '$input'"
                print_status "Available commands: r(restart both), c(client), s(server), l(logs), 1-4(specific logs), q(quit)"
                ;;
        esac
    done
}

# Main script logic
main() {
    local action=${1:-both}

    print_status "PokeCobbleClaim Mod Development Environment Restart Script"

    # Check if no arguments provided, start interactive mode
    if [ $# -eq 0 ]; then
        print_status "No arguments provided - starting interactive mode"
        echo
        interactive_mode
    fi

    print_status "Action: $action"
    echo

    case $action in
        "client")
            print_status "Restarting Minecraft Client..."
            kill_gradle_process "runclient"
            sleep 1
            start_gradle_task "runClient"
            wait_for_startup "runClient"
            ;;
        "server")
            print_status "Restarting Minecraft Server..."
            kill_gradle_process "runserver"
            sleep 1
            start_gradle_task "runServer"
            wait_for_startup "runServer"
            ;;
        "both")
            restart_both
            ;;
        "interactive"|"-i")
            interactive_mode
            ;;
        "logs"|"log"|"-l")
            shift  # Remove the first argument (action)
            local log_type=${1:-menu}
            local view_mode=${2:-tail}
            open_logs "$log_type" "$view_mode"
            ;;
        "client-log"|"cl")
            shift  # Remove the first argument (action)
            local view_mode=${1:-tail}
            open_logs "client-gradle" "$view_mode"
            ;;
        "server-log"|"sl")
            shift  # Remove the first argument (action)
            local view_mode=${1:-tail}
            open_logs "server-gradle" "$view_mode"
            ;;
        "runtime-log"|"rl")
            shift  # Remove the first argument (action)
            local view_mode=${1:-tail}
            open_logs "server-runtime" "$view_mode"
            ;;
        "debug-log"|"dl")
            shift  # Remove the first argument (action)
            local view_mode=${1:-tail}
            open_logs "server-debug" "$view_mode"
            ;;
        *)
            print_error "Invalid action: $action"
            echo "Usage: $0 [action] [options...]"
            echo
            echo "Restart actions:"
            echo "  client      - Restart only the Minecraft client"
            echo "  server      - Restart only the Minecraft server"
            echo "  both        - Restart both client and server (default)"
            echo "  interactive - Enter interactive mode"
            echo "  (no args)   - Start in interactive mode"
            echo
            echo "Log viewing actions:"
            echo "  logs [type] [mode] - Open logs with optional type and view mode"
            echo "  client-log [mode]  - View client gradle log"
            echo "  server-log [mode]  - View server gradle log"
            echo "  runtime-log [mode] - View server runtime log"
            echo "  debug-log [mode]   - View server debug log"
            echo
            echo "Log types: client-gradle (cg), server-gradle (sg), server-runtime (sr), server-debug (sd)"
            echo "View modes: tail (t), less (l), cat (c), editor (e)"
            echo
            echo "Examples:"
            echo "  $0 logs                    # Interactive log menu"
            echo "  $0 logs client-gradle tail # Follow client gradle log"
            echo "  $0 client-log less         # View client log with less"
            echo "  $0 runtime-log             # Follow server runtime log"
            exit 1
            ;;
    esac

    # Only show completion message for restart operations
    case $action in
        "client"|"server"|"both")
            echo
            print_success "Restart operation completed!"
            print_status "You can monitor the logs with:"
            echo "  tail -f runClient.log"
            echo "  tail -f runServer.log"
            echo "Or use: $0 logs"
            ;;
        "logs"|"log"|"-l"|"client-log"|"cl"|"server-log"|"sl"|"runtime-log"|"rl"|"debug-log"|"dl")
            # Log viewing completed, no additional message needed
            ;;
    esac
}

# Check if we're in the right directory
if [ ! -f "gradlew" ]; then
    print_error "gradlew not found. Please run this script from the project root directory."
    exit 1
fi

# Make gradlew executable if it isn't
if [ ! -x "gradlew" ]; then
    print_status "Making gradlew executable..."
    chmod +x gradlew
fi

# Run main function
main "$@"

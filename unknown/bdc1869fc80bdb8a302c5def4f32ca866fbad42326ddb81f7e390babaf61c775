package com.pokecobble.town.network.town;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.TownPlayer;
import com.pokecobble.town.TownPlayerRank;
import com.pokecobble.town.gui.NotificationManager;
import com.pokecobble.town.network.town.TownDataSynchronizer;
import com.pokecobble.town.network.NetworkConstants;
import com.pokecobble.town.network.NetworkManager;
import com.pokecobble.town.network.PacketValidator;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking;
import net.fabricmc.fabric.api.networking.v1.PacketSender;
import net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.network.ClientPlayNetworkHandler;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayNetworkHandler;
import net.minecraft.server.network.ServerPlayerEntity;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Handles network communication for town-related operations.
 */
public class TownNetworkHandler {

    /**
     * Registers client-side packet handlers.
     */
    @Environment(EnvType.CLIENT)
    public static void registerClientHandlers() {
        // Register town data response handler
        ClientPlayNetworking.registerGlobalReceiver(
                NetworkConstants.TOWN_DATA_RESPONSE,
                TownNetworkHandler::handleTownDataResponse
        );

        // Register town list response handler
        ClientPlayNetworking.registerGlobalReceiver(
                NetworkConstants.TOWN_LIST_RESPONSE,
                TownNetworkHandler::handleTownListResponse
        );

        // Register town creation response handler
        ClientPlayNetworking.registerGlobalReceiver(
                NetworkConstants.TOWN_CREATE_RESPONSE,
                TownNetworkHandler::handleTownCreateResponse
        );

        // Register town player list update handler
        ClientPlayNetworking.registerGlobalReceiver(
                NetworkConstants.TOWN_PLAYER_LIST_UPDATE,
                TownNetworkHandler::handleTownPlayerListUpdate
        );

        // Register town join response handler
        ClientPlayNetworking.registerGlobalReceiver(
                NetworkConstants.TOWN_JOIN_RESPONSE,
                TownNetworkHandler::handleTownJoinResponse
        );

        // Register town leave response handler
        ClientPlayNetworking.registerGlobalReceiver(
                NetworkConstants.TOWN_LEAVE_RESPONSE,
                TownNetworkHandler::handleTownLeaveResponse
        );
    }

    /**
     * Registers server-side packet handlers.
     */
    public static void registerServerHandlers() {
        // Register town data request handler
        ServerPlayNetworking.registerGlobalReceiver(
                NetworkConstants.TOWN_DATA_REQUEST,
                TownNetworkHandler::handleTownDataRequest
        );

        // Register town list request handler
        ServerPlayNetworking.registerGlobalReceiver(
                NetworkConstants.TOWN_LIST_REQUEST,
                TownNetworkHandler::handleTownListRequest
        );

        // Register town creation request handler
        ServerPlayNetworking.registerGlobalReceiver(
                NetworkConstants.TOWN_CREATE_REQUEST,
                TownNetworkHandler::handleTownCreateRequest
        );

        // Register town join request handler
        ServerPlayNetworking.registerGlobalReceiver(
                NetworkConstants.TOWN_JOIN_REQUEST,
                TownNetworkHandler::handleTownJoinRequest
        );

        // Register town leave request handler
        ServerPlayNetworking.registerGlobalReceiver(
                NetworkConstants.TOWN_LEAVE_REQUEST,
                TownNetworkHandler::handleTownLeaveRequest
        );
    }

    // Client-side methods

    /**
     * Requests town data from the server.
     */
    @Environment(EnvType.CLIENT)
    public static void requestTownData() {
        try {
            // Create packet buffer
            PacketByteBuf buf = NetworkManager.createPacket();

            // Add player UUID for validation
            if (MinecraftClient.getInstance().player != null) {
                buf.writeUuid(MinecraftClient.getInstance().player.getUuid());
            } else {
                return; // Can't send without a player
            }

            // Send packet to server
            NetworkManager.sendToServer(NetworkConstants.TOWN_DATA_REQUEST, buf);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error requesting town data: " + e.getMessage());
        }
    }

    /**
     * Requests town list from the server.
     */
    @Environment(EnvType.CLIENT)
    public static void requestTownList() {
        try {
            // Create packet buffer
            PacketByteBuf buf = NetworkManager.createPacket();

            // Add player UUID for validation
            if (MinecraftClient.getInstance().player != null) {
                buf.writeUuid(MinecraftClient.getInstance().player.getUuid());
            } else {
                return; // Can't send without a player
            }

            // Send packet to server
            NetworkManager.sendToServer(NetworkConstants.TOWN_LIST_REQUEST, buf);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error requesting town list: " + e.getMessage());
        }
    }

    /**
     * Sends a town creation request to the server.
     */
    @Environment(EnvType.CLIENT)
    public static void requestTownCreation(String name, String description, Town.JoinType joinType, int maxPlayers) {
        try {
            // Create packet buffer
            PacketByteBuf buf = NetworkManager.createPacket();

            // Add player UUID for validation
            if (MinecraftClient.getInstance().player != null) {
                buf.writeUuid(MinecraftClient.getInstance().player.getUuid());
            } else {
                return; // Can't send without a player
            }

            // Write town data
            buf.writeString(name);
            buf.writeString(description);
            buf.writeString(joinType.name());
            buf.writeInt(maxPlayers);

            // Send packet to server
            NetworkManager.sendToServer(NetworkConstants.TOWN_CREATE_REQUEST, buf);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error requesting town creation: " + e.getMessage());
        }
    }

    /**
     * Sends a town join request to the server.
     */
    @Environment(EnvType.CLIENT)
    public static void requestTownJoin(UUID townId) {
        try {
            // Create packet buffer
            PacketByteBuf buf = NetworkManager.createPacket();

            // Add player UUID for validation
            if (MinecraftClient.getInstance().player != null) {
                buf.writeUuid(MinecraftClient.getInstance().player.getUuid());
                Pokecobbleclaim.LOGGER.info("Sending town join request for player " + MinecraftClient.getInstance().player.getName().getString() + " to town " + townId);
            } else {
                Pokecobbleclaim.LOGGER.warn("Cannot send town join request - no player");
                return; // Can't send without a player
            }

            // Write town ID
            buf.writeUuid(townId);

            // Send packet to server
            NetworkManager.sendToServer(NetworkConstants.TOWN_JOIN_REQUEST, buf);
            Pokecobbleclaim.LOGGER.info("Town join request packet sent successfully");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error requesting town join: " + e.getMessage());
        }
    }

    /**
     * Sends a town leave request to the server.
     */
    @Environment(EnvType.CLIENT)
    public static void requestTownLeave() {
        try {
            // Create packet buffer
            PacketByteBuf buf = NetworkManager.createPacket();

            // Add player UUID for validation
            if (MinecraftClient.getInstance().player != null) {
                buf.writeUuid(MinecraftClient.getInstance().player.getUuid());
            } else {
                return; // Can't send without a player
            }

            // Send packet to server
            NetworkManager.sendToServer(NetworkConstants.TOWN_LEAVE_REQUEST, buf);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error requesting town leave: " + e.getMessage());
        }
    }

    // Client-side packet handlers

    /**
     * Handles town data response packets.
     */
    @Environment(EnvType.CLIENT)
    private static void handleTownDataResponse(MinecraftClient client, ClientPlayNetworkHandler handler,
                                              PacketByteBuf buf, PacketSender sender) {
        try {
            // Validate packet size
            PacketValidator.validatePacketSize(buf);

            // Read town data
            boolean hasTown = buf.readBoolean();

            if (hasTown) {
                // Read town UUID
                UUID townId = buf.readUuid();

                // Read town name
                String townName = buf.readString(NetworkConstants.MAX_STRING_LENGTH);

                // Read town description
                String townDescription = buf.readString(NetworkConstants.MAX_STRING_LENGTH);

                // Read town status
                boolean isOpen = buf.readBoolean();

                // Read player count
                int playerCount = buf.readInt();

                // Read max players
                int maxPlayers = buf.readInt();

                // Read player rank
                int rankOrdinal = buf.readInt();
                TownPlayerRank playerRank = TownPlayerRank.values()[rankOrdinal];

                // Read player permissions
                int permissionCategoryCount = buf.readInt();
                Map<String, Map<String, Boolean>> permissions = new HashMap<>();

                for (int i = 0; i < permissionCategoryCount; i++) {
                    String category = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                    int permissionCount = buf.readInt();
                    Map<String, Boolean> categoryPermissions = new HashMap<>();

                    for (int j = 0; j < permissionCount; j++) {
                        String permission = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                        boolean value = buf.readBoolean();
                        categoryPermissions.put(permission, value);
                    }

                    permissions.put(category, categoryPermissions);
                }

                // Read player list
                int actualPlayerCount = buf.readInt();
                List<TownPlayer> townPlayers = new ArrayList<>();

                for (int i = 0; i < actualPlayerCount; i++) {
                    UUID playerId = buf.readUuid();
                    String playerName = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                    String rankStr = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                    boolean isOnline = buf.readBoolean();

                    // Parse rank
                    TownPlayerRank rank;
                    try {
                        rank = TownPlayerRank.valueOf(rankStr);
                        Pokecobbleclaim.LOGGER.debug("Parsed rank for player " + playerName + ": " + rankStr + " -> " + rank.getDisplayName());
                    } catch (IllegalArgumentException e) {
                        rank = TownPlayerRank.MEMBER; // Default fallback
                        Pokecobbleclaim.LOGGER.warn("Invalid rank string '" + rankStr + "' for player " + playerName + ", defaulting to MEMBER");
                    }

                    // Create TownPlayer
                    TownPlayer townPlayer = new TownPlayer(playerId, playerName, rank, isOnline);
                    townPlayers.add(townPlayer);
                }

                // Update town data in TownManager
                Town town = new Town(townId, townName);
                town.setDescription(townDescription);
                town.setOpen(isOpen);
                town.setMaxPlayers(maxPlayers);

                // Add all players to the town
                for (TownPlayer townPlayer : townPlayers) {
                    town.addPlayer(townPlayer);
                }

                // Clear display player count since we now have the actual player list
                town.clearDisplayPlayerCount();

                // Set permissions for the current player if they're in the town
                if (client.player != null) {
                    TownPlayer currentPlayer = town.getPlayer(client.player.getUuid());
                    if (currentPlayer != null) {
                        // Set permissions for the current player
                        for (Map.Entry<String, Map<String, Boolean>> entry : permissions.entrySet()) {
                            currentPlayer.setCategoryPermissions(entry.getKey(), entry.getValue());
                        }
                    }
                }

                // Log player count for debugging
                Pokecobbleclaim.LOGGER.debug("Received town data for " + townName + " with " + actualPlayerCount + " players");

                // Update the town in the TownManager
                TownManager.getInstance().addTown(town);

                // IMPORTANT: Also update client-side town cache and player town association
                if (client.player != null) {
                    // Add the town to the client-side cache
                    com.pokecobble.town.client.ClientTownManager.getInstance().updateTown(town, town.getDataVersion());

                    // Set the player's town association
                    com.pokecobble.town.client.ClientTownManager.getInstance().setPlayerTown(townId, town.getDataVersion());
                    Pokecobbleclaim.LOGGER.debug("Updated client-side town cache and player association for town: " + townName);
                }

                // Notify the player
                NotificationManager.getInstance().addInfoNotification("Town data updated: " + townName);
            } else {
                // Player is not in a town
                if (client.player != null) {
                    TownManager.getInstance().removePlayerFromTown(client.player.getUuid());
                }

                // Notify the player
                NotificationManager.getInstance().addInfoNotification("You are not in a town");
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling town data response: " + e.getMessage());
        }
    }

    /**
     * Handles town list response packets.
     */
    @Environment(EnvType.CLIENT)
    private static void handleTownListResponse(MinecraftClient client, ClientPlayNetworkHandler handler,
                                              PacketByteBuf buf, PacketSender sender) {
        try {
            // Validate packet size
            PacketValidator.validatePacketSize(buf);

            // Read town count
            int townCount = buf.readInt();
            PacketValidator.validateListSize(townCount);

            // Log for debugging
            Pokecobbleclaim.LOGGER.info("Received town list response with " + townCount + " towns");

            // Read town data
            List<Town> towns = new ArrayList<>();

            for (int i = 0; i < townCount; i++) {
                // Read town UUID
                UUID townId = buf.readUuid();

                // Read town name
                String townName = buf.readString(NetworkConstants.MAX_STRING_LENGTH);

                // Read town description
                String townDescription = buf.readString(NetworkConstants.MAX_STRING_LENGTH);

                // Read town status
                boolean isOpen = buf.readBoolean();

                // Read player count
                int playerCount = buf.readInt();

                // Read max players
                int maxPlayers = buf.readInt();

                // Read join type
                String joinTypeStr = buf.readString();

                // Parse join type
                Town.JoinType joinType;
                try {
                    joinType = Town.JoinType.valueOf(joinTypeStr);
                } catch (IllegalArgumentException e) {
                    joinType = Town.JoinType.OPEN; // Default fallback
                }

                // Create town object
                Town town = new Town(townId, townName);
                town.setDescription(townDescription);
                town.setOpen(isOpen);
                town.setJoinType(joinType);
                town.setMaxPlayers(maxPlayers);

                // Set the display player count for town list display
                town.setDisplayPlayerCount(playerCount);

                // Log player count for debugging
                Pokecobbleclaim.LOGGER.debug("Received town list entry: " + townName + " (" + playerCount + "/" + maxPlayers + " players)");

                towns.add(town);
            }

            // Log received towns for debugging
            if (!towns.isEmpty()) {
                StringBuilder townNames = new StringBuilder("Received towns: ");
                for (Town town : towns) {
                    townNames.append(town.getName()).append(", ");
                }
                Pokecobbleclaim.LOGGER.info(townNames.toString());
            }

            // Update town list in TownManager
            TownManager.getInstance().setTownList(towns);

            // IMPORTANT: Also update the ClientTownManager with the town list
            // This ensures that client-side caching works properly and ModernTownScreen displays the correct towns
            com.pokecobble.town.client.ClientTownManager.getInstance().updateTownList(towns, 1);

            // Notify the player
            NotificationManager.getInstance().addInfoNotification("Town list updated: " + townCount + " towns");

            // Notify town list update callbacks
            TownDataSynchronizer.notifyTownListUpdated();
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling town list response: " + e.getMessage());
        }
    }

    /**
     * Handles town creation response packets.
     */
    @Environment(EnvType.CLIENT)
    private static void handleTownCreateResponse(MinecraftClient client, ClientPlayNetworkHandler handler,
                                               PacketByteBuf buf, PacketSender sender) {
        try {
            // Validate packet size
            PacketValidator.validatePacketSize(buf);

            // Read response data
            boolean success = buf.readBoolean();
            String message = buf.readString();

            if (success) {
                // Read town data
                UUID townId = buf.readUuid();
                String townName = buf.readString();
                String townDescription = buf.readString();
                String joinTypeStr = buf.readString();
                int maxPlayers = buf.readInt();

                // Parse join type
                Town.JoinType joinType;
                try {
                    joinType = Town.JoinType.valueOf(joinTypeStr);
                } catch (IllegalArgumentException e) {
                    joinType = Town.JoinType.OPEN; // Default fallback
                }

                // Create town object
                Town town = new Town(townId, townName);
                town.setDescription(townDescription);
                town.setJoinType(joinType);
                town.setMaxPlayers(maxPlayers);

                // Add the current player to the town as owner
                if (client.player != null) {
                    TownPlayer townPlayer = new TownPlayer(client.player.getUuid(), client.player.getName().getString(), TownPlayerRank.OWNER);
                    town.addPlayer(townPlayer);
                }

                // Update the town in the TownManager
                TownManager.getInstance().addTown(town);

                // IMPORTANT: Update client-side town cache and player town association
                if (client.player != null) {
                    // Add the town to the client-side cache first
                    com.pokecobble.town.client.ClientTownManager.getInstance().updateTown(town, town.getDataVersion());

                    // Then set the player's town association
                    com.pokecobble.town.client.ClientTownManager.getInstance().setPlayerTown(townId, town.getDataVersion());
                    Pokecobbleclaim.LOGGER.info("Updated client player town association: player " + client.player.getName().getString() + " is now in town " + townName + " (" + townId + ")");
                }

                // Notify the player
                NotificationManager.getInstance().addSuccessNotification("Town created successfully: " + townName);

                // Request updated town list to ensure all clients are in sync
                requestTownList();
            } else {
                // Show error message
                NotificationManager.getInstance().addErrorNotification("Failed to create town: " + message);

                // If the error is "You are already in a town", request fresh data to sync client state
                if (message.contains("already in a town")) {
                    Pokecobbleclaim.LOGGER.info("Player is already in a town, requesting fresh town data to sync client state");
                    requestTownData();

                    // Also refresh UI components to update MyTownScreen access
                    refreshUIComponents();
                }
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling town creation response: " + e.getMessage());
        }
    }

    /**
     * Handles town player list update packets.
     */
    @Environment(EnvType.CLIENT)
    private static void handleTownPlayerListUpdate(MinecraftClient client, ClientPlayNetworkHandler handler,
                                                 PacketByteBuf buf, PacketSender sender) {
        try {
            // Validate packet size
            PacketValidator.validatePacketSize(buf);

            // Read town data
            UUID townId = buf.readUuid();
            String townName = buf.readString();
            int playerCount = buf.readInt();
            int maxPlayers = buf.readInt();

            // Read player list
            int playerListSize = buf.readInt();
            List<TownPlayer> townPlayers = new ArrayList<>();

            for (int i = 0; i < playerListSize; i++) {
                UUID playerId = buf.readUuid();
                String playerName = buf.readString();
                String rankStr = buf.readString();
                boolean isOnline = buf.readBoolean();

                // Parse rank
                TownPlayerRank rank;
                try {
                    rank = TownPlayerRank.valueOf(rankStr);
                } catch (IllegalArgumentException e) {
                    rank = TownPlayerRank.MEMBER; // Default fallback
                }

                // Create TownPlayer
                TownPlayer townPlayer = new TownPlayer(playerId, playerName, rank, isOnline);
                townPlayers.add(townPlayer);
            }

            // Get or create the town
            Town town = TownManager.getInstance().getTownById(townId);
            if (town == null) {
                town = new Town(townId, townName);
                TownManager.getInstance().addTown(town);
            }

            // Update town data
            town.setName(townName);
            town.setMaxPlayers(maxPlayers);

            // Clear existing players and add new ones
            town.clearPlayers();
            for (TownPlayer townPlayer : townPlayers) {
                town.addPlayer(townPlayer);
            }

            // Clear display player count since we now have the actual player list
            town.clearDisplayPlayerCount();

            // Update client cache
            com.pokecobble.town.client.ClientTownManager.getInstance().updateTown(town, town.getDataVersion());

            // Refresh UI components that display player counts and lists
            refreshUIComponents();

            Pokecobbleclaim.LOGGER.debug("Updated player list for town " + townName + " (" + playerCount + " players)");

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling town player list update: " + e.getMessage());
        }
    }

    /**
     * Handles town join response packets.
     */
    @Environment(EnvType.CLIENT)
    private static void handleTownJoinResponse(MinecraftClient client, ClientPlayNetworkHandler handler,
                                             PacketByteBuf buf, PacketSender sender) {
        try {
            // Validate packet size
            PacketValidator.validatePacketSize(buf);

            // Read response data
            boolean success = buf.readBoolean();
            String message = buf.readString();

            Pokecobbleclaim.LOGGER.info("Received town join response: success=" + success + ", message=" + message);

            if (success) {
                // Read additional data for successful join
                UUID townId = buf.readUuid();
                String townName = buf.readString();

                // IMPORTANT: Update client-side player town association
                if (client.player != null) {
                    com.pokecobble.town.client.ClientTownManager.getInstance().setPlayerTown(townId, 1); // Use version 1 as default
                    Pokecobbleclaim.LOGGER.info("Updated client player town association: player " + client.player.getName().getString() + " joined town " + townName + " (" + townId + ")");
                }

                // Show success message
                NotificationManager.getInstance().addSuccessNotification(message);

                // The server now sends player town membership data immediately in the join response
                // No need to make additional requests that could be rate-limited
                // The server will sync all necessary data automatically

                Pokecobbleclaim.LOGGER.info("Town join successful - server will sync membership data automatically");

                // Refresh UI components
                refreshUIComponents();

                Pokecobbleclaim.LOGGER.info("Successfully joined town: " + message);
            } else {
                // Show error message
                NotificationManager.getInstance().addErrorNotification(message);

                // If the error is "You are already in a town", request fresh data to sync client state
                if (message.contains("already in a town")) {
                    Pokecobbleclaim.LOGGER.info("Player is already in a town, requesting fresh town data to sync client state");
                    requestTownData();

                    // Also refresh UI components to update MyTownScreen access
                    refreshUIComponents();
                }

                Pokecobbleclaim.LOGGER.warn("Failed to join town: " + message);
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling town join response: " + e.getMessage());
        }
    }

    /**
     * Handles town leave response packets.
     */
    @Environment(EnvType.CLIENT)
    private static void handleTownLeaveResponse(MinecraftClient client, ClientPlayNetworkHandler handler,
                                              PacketByteBuf buf, PacketSender sender) {
        try {
            // Validate packet size
            PacketValidator.validatePacketSize(buf);

            // Read response data
            boolean success = buf.readBoolean();
            String message = buf.readString();

            if (success) {
                // Show success message
                NotificationManager.getInstance().addSuccessNotification(message);

                // IMPORTANT: Immediately clear client-side town association
                if (client.player != null) {
                    // Clear the player's town association in ClientTownManager
                    com.pokecobble.town.client.ClientTownManager.getInstance().setPlayerTown(null, -1);

                    // Remove the player from TownManager
                    TownManager.getInstance().removePlayerFromTown(client.player.getUuid());

                    Pokecobbleclaim.LOGGER.info("Cleared client-side town association for player " + client.player.getName().getString());
                }

                // Request updated town list and player data to ensure all clients are in sync
                requestTownList();

                // Refresh UI components
                refreshUIComponents();

                // Fire a town data update event to refresh all UI components immediately
                com.pokecobble.town.client.ClientSyncManager.getInstance().fireEvent("town_updated",
                    java.util.Map.of("action", "player_left", "timestamp", System.currentTimeMillis()));

                Pokecobbleclaim.LOGGER.info("Successfully left town: " + message);
            } else {
                // Show error message
                NotificationManager.getInstance().addErrorNotification(message);

                Pokecobbleclaim.LOGGER.warn("Failed to leave town: " + message);
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling town leave response: " + e.getMessage());
        }
    }

    // Server-side packet handlers

    /**
     * Handles town data request packets.
     */
    private static void handleTownDataRequest(MinecraftServer server, ServerPlayerEntity player,
                                             ServerPlayNetworkHandler handler, PacketByteBuf buf,
                                             PacketSender sender) {
        try {
            // Validate packet size
            PacketValidator.validatePacketSize(buf);

            // Read player UUID for validation
            UUID requestingPlayerId = buf.readUuid();

            // Validate that the requesting player is the same as the sender
            if (!player.getUuid().equals(requestingPlayerId)) {
                Pokecobbleclaim.LOGGER.warn("Player " + player.getName().getString() + " tried to request town data for another player");
                return;
            }

            // Get the player's town
            Town playerTown = TownManager.getInstance().getPlayerTown(player.getUuid());

            // Create response packet
            PacketByteBuf responseBuf = NetworkManager.createPacket();

            if (playerTown != null) {
                // Player is in a town
                responseBuf.writeBoolean(true);

                // Write town UUID
                responseBuf.writeUuid(playerTown.getId());

                // Write town name
                responseBuf.writeString(playerTown.getName());

                // Write town description
                responseBuf.writeString(playerTown.getDescription());

                // Write town status
                responseBuf.writeBoolean(playerTown.isOpen());

                // Write player count
                responseBuf.writeInt(playerTown.getPlayerCount());

                // Write max players
                responseBuf.writeInt(playerTown.getMaxPlayers());

                // Write player rank
                TownPlayerRank playerRank = playerTown.getPlayerRank(player.getUuid());
                responseBuf.writeInt(playerRank.ordinal());

                // Write player permissions
                TownPlayer townPlayer = playerTown.getPlayer(player.getUuid());
                Map<String, Map<String, Boolean>> permissions = townPlayer.getAllPermissions();

                responseBuf.writeInt(permissions.size());

                for (Map.Entry<String, Map<String, Boolean>> entry : permissions.entrySet()) {
                    responseBuf.writeString(entry.getKey());

                    Map<String, Boolean> categoryPermissions = entry.getValue();
                    responseBuf.writeInt(categoryPermissions.size());

                    for (Map.Entry<String, Boolean> permEntry : categoryPermissions.entrySet()) {
                        responseBuf.writeString(permEntry.getKey());
                        responseBuf.writeBoolean(permEntry.getValue());
                    }
                }

                // Write complete player list for the town
                List<UUID> townPlayers = playerTown.getPlayers();
                responseBuf.writeInt(townPlayers.size());

                for (UUID playerId : townPlayers) {
                    responseBuf.writeUuid(playerId);

                    // Get player name
                    String playerName = TownManager.getInstance().getPlayerName(playerId);
                    responseBuf.writeString(playerName);

                    // Get player rank - use the town's getPlayerRank method for more reliable rank lookup
                    TownPlayerRank otherPlayerRank = playerTown.getPlayerRank(playerId);
                    if (otherPlayerRank == null) {
                        // If no rank found, default to MEMBER
                        otherPlayerRank = TownPlayerRank.MEMBER;
                        // Set the rank in the town to ensure consistency
                        playerTown.setPlayerRank(playerId, otherPlayerRank);
                        Pokecobbleclaim.LOGGER.debug("Set default MEMBER rank for player " + playerId + " in town " + playerTown.getName());
                    }
                    responseBuf.writeString(otherPlayerRank.name());

                    // Check if player is online
                    boolean isOnline = server.getPlayerManager().getPlayer(playerId) != null;
                    responseBuf.writeBoolean(isOnline);
                }
            } else {
                // Player is not in a town
                responseBuf.writeBoolean(false);
            }

            // Send response to player
            NetworkManager.sendToPlayer(player, NetworkConstants.TOWN_DATA_RESPONSE, responseBuf);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling town data request: " + e.getMessage());
        }
    }

    /**
     * Handles town list request packets.
     */
    private static void handleTownListRequest(MinecraftServer server, ServerPlayerEntity player,
                                            ServerPlayNetworkHandler handler, PacketByteBuf buf,
                                            PacketSender sender) {
        try {
            // Validate packet size
            PacketValidator.validatePacketSize(buf);

            // Read player UUID for validation
            UUID requestingPlayerId = buf.readUuid();

            // Validate that the requesting player is the same as the sender
            if (!player.getUuid().equals(requestingPlayerId)) {
                Pokecobbleclaim.LOGGER.warn("Player " + player.getName().getString() + " tried to request town list for another player");
                return;
            }

            // Get all towns
            Collection<Town> townCollection = TownManager.getInstance().getAllTowns();
            List<Town> towns = new ArrayList<>(townCollection);

            // Log for debugging
            Pokecobbleclaim.LOGGER.info("Sending town list to player " + player.getName().getString() + " with " + towns.size() + " towns");
            if (!towns.isEmpty()) {
                StringBuilder townNames = new StringBuilder("Towns being sent: ");
                for (Town town : towns) {
                    townNames.append(town.getName()).append(", ");
                }
                Pokecobbleclaim.LOGGER.info(townNames.toString());
            }

            // Create response packet
            PacketByteBuf responseBuf = NetworkManager.createPacket();

            // Write town count
            responseBuf.writeInt(towns.size());

            // Write town data
            for (Town town : towns) {
                // Write town UUID
                responseBuf.writeUuid(town.getId());

                // Write town name
                responseBuf.writeString(town.getName());

                // Write town description
                responseBuf.writeString(town.getDescription());

                // Write town status
                responseBuf.writeBoolean(town.isOpen());

                // Write player count
                responseBuf.writeInt(town.getPlayerCount());

                // Write max players
                responseBuf.writeInt(town.getMaxPlayers());

                // Write join type
                responseBuf.writeString(town.getJoinType().name());
            }

            // Send response to player
            NetworkManager.sendToPlayer(player, NetworkConstants.TOWN_LIST_RESPONSE, responseBuf);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling town list request: " + e.getMessage());
        }
    }

    /**
     * Handles town creation request packets.
     */
    private static void handleTownCreateRequest(MinecraftServer server, ServerPlayerEntity player,
                                              ServerPlayNetworkHandler handler, PacketByteBuf buf,
                                              PacketSender sender) {
        try {
            // Validate packet size
            PacketValidator.validatePacketSize(buf);

            // Read player UUID for validation
            UUID requestingPlayerId = buf.readUuid();

            // Validate that the requesting player is the same as the sender
            if (!player.getUuid().equals(requestingPlayerId)) {
                Pokecobbleclaim.LOGGER.warn("Player " + player.getName().getString() + " tried to create town for another player");
                return;
            }

            // Read town data
            String name = buf.readString();
            String description = buf.readString();
            String joinTypeStr = buf.readString();
            int maxPlayers = buf.readInt();

            // Parse join type
            Town.JoinType joinType;
            try {
                joinType = Town.JoinType.valueOf(joinTypeStr);
            } catch (IllegalArgumentException e) {
                joinType = Town.JoinType.OPEN; // Default fallback
            }

            // Create response packet
            PacketByteBuf responseBuf = NetworkManager.createPacket();

            // Validate town name
            if (name == null || name.trim().isEmpty()) {
                responseBuf.writeBoolean(false);
                responseBuf.writeString("Town name cannot be empty");
                NetworkManager.sendToPlayer(player, NetworkConstants.TOWN_CREATE_RESPONSE, responseBuf);
                return;
            }

            // Check if player is already in a town
            if (TownManager.getInstance().getPlayerTownId(player.getUuid()) != null) {
                responseBuf.writeBoolean(false);
                responseBuf.writeString("You are already in a town");
                NetworkManager.sendToPlayer(player, NetworkConstants.TOWN_CREATE_RESPONSE, responseBuf);
                return;
            }

            // Check if town name is taken
            if (TownManager.getInstance().isTownNameTaken(name)) {
                responseBuf.writeBoolean(false);
                responseBuf.writeString("Town name is already taken");
                NetworkManager.sendToPlayer(player, NetworkConstants.TOWN_CREATE_RESPONSE, responseBuf);
                return;
            }

            // Create the town with the player as mayor
            Town town = TownManager.getInstance().createTownWithMayor(name, description, joinType, maxPlayers, player.getUuid());

            if (town != null) {
                // Success response
                responseBuf.writeBoolean(true);
                responseBuf.writeString("Town created successfully");
                responseBuf.writeUuid(town.getId());
                responseBuf.writeString(town.getName());
                responseBuf.writeString(town.getDescription());
                responseBuf.writeString(town.getJoinType().name());
                responseBuf.writeInt(town.getMaxPlayers());

                Pokecobbleclaim.LOGGER.info("Player " + player.getName().getString() + " created town: " + name);

                // CRITICAL: Send response first, then sync player town data
                NetworkManager.sendToPlayer(player, NetworkConstants.TOWN_CREATE_RESPONSE, responseBuf);

                // Sync the player's town data to ensure client receives proper membership information
                server.execute(() -> {
                    TownDataSynchronizer.syncPlayerTownData(server, player.getUuid());
                    Pokecobbleclaim.LOGGER.info("Synchronized town data for newly created town " + town.getName() + " to player " + player.getName().getString());
                });

                return; // Early return since we already sent the response
            } else {
                // Failure response
                responseBuf.writeBoolean(false);
                responseBuf.writeString("Failed to create town");
            }

            // Send response to player (only for failure case now)
            NetworkManager.sendToPlayer(player, NetworkConstants.TOWN_CREATE_RESPONSE, responseBuf);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling town creation request: " + e.getMessage());

            // Send error response
            try {
                PacketByteBuf errorBuf = NetworkManager.createPacket();
                errorBuf.writeBoolean(false);
                errorBuf.writeString("Server error occurred");
                NetworkManager.sendToPlayer(player, NetworkConstants.TOWN_CREATE_RESPONSE, errorBuf);
            } catch (Exception sendError) {
                Pokecobbleclaim.LOGGER.error("Error sending error response: " + sendError.getMessage());
            }
        }
    }

    /**
     * Handles town join request packets.
     */
    private static void handleTownJoinRequest(MinecraftServer server, ServerPlayerEntity player,
                                            ServerPlayNetworkHandler handler, PacketByteBuf buf,
                                            PacketSender sender) {
        try {
            // Validate packet size
            PacketValidator.validatePacketSize(buf);

            // Read player UUID for validation
            UUID requestingPlayerId = buf.readUuid();

            // Validate that the requesting player is the same as the sender
            if (!player.getUuid().equals(requestingPlayerId)) {
                Pokecobbleclaim.LOGGER.warn("Player " + player.getName().getString() + " tried to join town for another player");
                return;
            }

            // Read town ID
            UUID townId = buf.readUuid();

            Pokecobbleclaim.LOGGER.info("Processing town join request from player " + player.getName().getString() + " for town " + townId);

            // Create response packet
            PacketByteBuf responseBuf = NetworkManager.createPacket();

            // Check if player is already in a town
            UUID currentTownId = TownManager.getInstance().getPlayerTownId(player.getUuid());
            if (currentTownId != null) {
                Pokecobbleclaim.LOGGER.info("Player " + player.getName().getString() + " is already in town " + currentTownId);
                responseBuf.writeBoolean(false);
                responseBuf.writeString("You are already in a town");
                NetworkManager.sendToPlayer(player, NetworkConstants.TOWN_JOIN_RESPONSE, responseBuf);
                return;
            }

            // Get the town
            Town town = TownManager.getInstance().getTownById(townId);
            if (town == null) {
                Pokecobbleclaim.LOGGER.warn("Town not found: " + townId);
                responseBuf.writeBoolean(false);
                responseBuf.writeString("Town not found");
                NetworkManager.sendToPlayer(player, NetworkConstants.TOWN_JOIN_RESPONSE, responseBuf);
                return;
            }

            Pokecobbleclaim.LOGGER.info("Found town: " + town.getName() + " (players: " + town.getPlayerCount() + "/" + town.getMaxPlayers() + ", type: " + town.getJoinType() + ")");

            // Check if town is full
            if (town.getPlayerCount() >= town.getMaxPlayers()) {
                Pokecobbleclaim.LOGGER.info("Town " + town.getName() + " is full (" + town.getPlayerCount() + "/" + town.getMaxPlayers() + ")");
                responseBuf.writeBoolean(false);
                responseBuf.writeString("Town is full");
                NetworkManager.sendToPlayer(player, NetworkConstants.TOWN_JOIN_RESPONSE, responseBuf);
                return;
            }

            // Check if town is open or invite-only
            Town.JoinType joinType = town.getJoinType();
            if (joinType == null) {
                // Default to OPEN if join type is null
                joinType = Town.JoinType.OPEN;
                town.setJoinType(joinType);
                Pokecobbleclaim.LOGGER.info("Town " + town.getName() + " had null join type, defaulting to OPEN");
            }

            if (joinType == Town.JoinType.CLOSED) {
                Pokecobbleclaim.LOGGER.info("Town " + town.getName() + " is closed to new members");
                responseBuf.writeBoolean(false);
                responseBuf.writeString("Town is closed to new members");
                NetworkManager.sendToPlayer(player, NetworkConstants.TOWN_JOIN_RESPONSE, responseBuf);
                return;
            }

            // For invite-only towns, we should check if the player has an invitation
            // For now, we'll allow joining invite-only towns directly (this can be enhanced later)
            if (joinType == Town.JoinType.INVITE_ONLY) {
                Pokecobbleclaim.LOGGER.info("Player " + player.getName().getString() + " attempting to join invite-only town " + town.getName() + " - allowing for now");
            }

            // Add player to town
            Pokecobbleclaim.LOGGER.info("Attempting to add player " + player.getName().getString() + " to town " + town.getName());
            boolean success = TownManager.getInstance().addPlayerToTown(player.getUuid(), townId);

            // If the standard method failed, try with a more direct approach
            if (!success) {
                Pokecobbleclaim.LOGGER.warn("Standard addPlayerToTown failed, attempting direct approach");
                success = addPlayerToTownDirect(player, town);
            }

            if (success) {
                // Success response
                responseBuf.writeBoolean(true);
                responseBuf.writeString("Successfully joined " + town.getName());
                responseBuf.writeUuid(townId); // Include town ID for client-side association
                responseBuf.writeString(town.getName()); // Include town name for client-side display

                Pokecobbleclaim.LOGGER.info("SUCCESS: Player " + player.getName().getString() + " joined town: " + town.getName());

                // CRITICAL: Send response first, then sync player town data
                NetworkManager.sendToPlayer(player, NetworkConstants.TOWN_JOIN_RESPONSE, responseBuf);

                // IMMEDIATE: Send player town membership data right now, not later
                // This ensures the client knows they're in a town immediately
                TownDataSynchronizer.sendPlayerTownMembership(player, town.getId(), town.getDataVersion());

                // Sync the player's town data to ensure client receives proper membership information
                server.execute(() -> {
                    TownDataSynchronizer.syncPlayerTownData(server, player.getUuid());
                    Pokecobbleclaim.LOGGER.info("Synchronized town data for joined town " + town.getName() + " to player " + player.getName().getString());
                });

                return; // Early return since we already sent the response
            } else {
                // Failure response
                responseBuf.writeBoolean(false);
                responseBuf.writeString("Failed to join town");

                Pokecobbleclaim.LOGGER.warn("FAILED: Player " + player.getName().getString() + " could not join town: " + town.getName());
            }

            // Send response to player (only for failure case now)
            NetworkManager.sendToPlayer(player, NetworkConstants.TOWN_JOIN_RESPONSE, responseBuf);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling town join request: " + e.getMessage());

            // Send error response
            try {
                PacketByteBuf errorBuf = NetworkManager.createPacket();
                errorBuf.writeBoolean(false);
                errorBuf.writeString("Server error occurred");
                NetworkManager.sendToPlayer(player, NetworkConstants.TOWN_JOIN_RESPONSE, errorBuf);
            } catch (Exception sendError) {
                Pokecobbleclaim.LOGGER.error("Error sending error response: " + sendError.getMessage());
            }
        }
    }

    /**
     * Handles town leave request packets.
     */
    private static void handleTownLeaveRequest(MinecraftServer server, ServerPlayerEntity player,
                                             ServerPlayNetworkHandler handler, PacketByteBuf buf,
                                             PacketSender sender) {
        try {
            // Validate packet size
            PacketValidator.validatePacketSize(buf);

            // Read player UUID for validation
            UUID requestingPlayerId = buf.readUuid();

            // Validate that the requesting player is the same as the sender
            if (!player.getUuid().equals(requestingPlayerId)) {
                Pokecobbleclaim.LOGGER.warn("Player " + player.getName().getString() + " tried to leave town for another player");
                return;
            }

            // Create response packet
            PacketByteBuf responseBuf = NetworkManager.createPacket();

            // Check if player is in a town
            UUID townId = TownManager.getInstance().getPlayerTownId(player.getUuid());
            if (townId == null) {
                responseBuf.writeBoolean(false);
                responseBuf.writeString("You are not in a town");
                NetworkManager.sendToPlayer(player, NetworkConstants.TOWN_LEAVE_RESPONSE, responseBuf);
                return;
            }

            // Get the town
            Town town = TownManager.getInstance().getTownById(townId);
            String townName = town != null ? town.getName() : "Unknown";

            // Remove player from town
            boolean success = TownManager.getInstance().removePlayerFromTown(player.getUuid());

            if (success) {
                // Success response
                responseBuf.writeBoolean(true);
                responseBuf.writeString("Successfully left " + townName);

                // Send player town membership update to clear the player's town association
                TownDataSynchronizer.sendPlayerTownMembership(player, null, -1);

                // Synchronize player data to all clients to update the leaving player's status
                com.pokecobble.town.network.player.PlayerDataSynchronizer.syncPlayerData(player.getServer(), player.getUuid());

                Pokecobbleclaim.LOGGER.info("Player " + player.getName().getString() + " left town: " + townName);
            } else {
                // Failure response
                responseBuf.writeBoolean(false);
                responseBuf.writeString("Failed to leave town");
            }

            // Send response to player
            NetworkManager.sendToPlayer(player, NetworkConstants.TOWN_LEAVE_RESPONSE, responseBuf);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling town leave request: " + e.getMessage());

            // Send error response
            try {
                PacketByteBuf errorBuf = NetworkManager.createPacket();
                errorBuf.writeBoolean(false);
                errorBuf.writeString("Server error occurred");
                NetworkManager.sendToPlayer(player, NetworkConstants.TOWN_LEAVE_RESPONSE, errorBuf);
            } catch (Exception sendError) {
                Pokecobbleclaim.LOGGER.error("Error sending error response: " + sendError.getMessage());
            }
        }
    }

    /**
     * Refreshes UI components that display town player counts and lists.
     * This method triggers updates for all relevant UI components when player data changes.
     */
    @Environment(EnvType.CLIENT)
    private static void refreshUIComponents() {
        try {
            // Refresh the UI data refresh manager
            com.pokecobble.ui.UIDataRefreshManager.getInstance().forceRefreshAll();

            // Trigger town list update callbacks
            TownDataSynchronizer.notifyTownListUpdated();

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error refreshing UI components: " + e.getMessage());
        }
    }

    /**
     * Directly adds a player to a town with more detailed error handling.
     * This is a fallback method when the standard addPlayerToTown fails.
     */
    private static boolean addPlayerToTownDirect(ServerPlayerEntity player, Town town) {
        try {
            UUID playerId = player.getUuid();
            String playerName = player.getName().getString();

            Pokecobbleclaim.LOGGER.info("Direct approach: Adding player " + playerName + " (" + playerId + ") to town " + town.getName());

            // Check if player is already in the town
            if (town.getPlayer(playerId) != null) {
                Pokecobbleclaim.LOGGER.warn("Player " + playerName + " is already in town " + town.getName());
                return false;
            }

            // Check if town is full
            if (town.getPlayerCount() >= town.getMaxPlayers()) {
                Pokecobbleclaim.LOGGER.warn("Town " + town.getName() + " is full (" + town.getPlayerCount() + "/" + town.getMaxPlayers() + ")");
                return false;
            }

            // Create TownPlayer with correct rank
            TownPlayerRank rank = town.getPlayerCount() == 0 ? TownPlayerRank.OWNER : TownPlayerRank.MEMBER;
            TownPlayer townPlayer = new TownPlayer(playerId, playerName, rank);

            // Add player to town
            town.addPlayer(townPlayer);

            // Update player-town mapping in TownManager
            TownManager.getInstance().updatePlayerTownMapping(playerId, town.getId());

            // Save data
            TownManager.getInstance().handlePlayerTownMembershipChange(playerId, null, town.getId(), "joined town " + town.getName() + " (direct)");

            // Broadcast updates
            TownManager.getInstance().broadcastTownPlayerListUpdate(town);
            TownDataSynchronizer.broadcastTownListUpdate(player.getServer());

            Pokecobbleclaim.LOGGER.info("Successfully added player " + playerName + " to town " + town.getName() + " using direct approach");
            return true;

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error in direct addPlayerToTown: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
}
